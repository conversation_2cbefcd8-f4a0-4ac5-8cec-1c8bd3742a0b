# Lo-Fi Video Generator

A CLI tool for generating lo-fi music videos with royalty-free music and style-specific visuals.

## Features

- 🎵 **Lo-Fi Music Discovery**: Curated lo-fi tracks from Freesound.org
- 🎬 **Video Generation**: Automatic video creation with style-specific visuals
- 🎨 **Multiple Styles**: 10 different lo-fi styles (calming, upbeat, chill, etc.)
- 📜 **License Compliance**: Automatic attribution for Creative Commons tracks
- 💾 **Smart Caching**: Local caching for better performance
- ⚡ **CLI Interface**: Simple command-line interface for quick video generation

## Available Lo-Fi Styles

- **upbeat** - Energetic, positive, bouncy lo-fi beats
- **calming** - Peaceful, serene, tranquil soundscapes
- **chill** - Laid-back, relaxed, mellow vibes
- **dreamy** - Ethereal, floating, atmospheric sounds
- **nostalgic** - Vintage, retro, warm memories
- **focus** - Concentration, work, productivity music
- **study** - Reading, learning, background ambience
- **relaxing** - Stress-relief, meditation, zen
- **ambient** - Atmospheric, textural soundscapes
- **jazzy** - Jazz-influenced, swing, blues elements

## Quick Start

1. **Run setup**:
   ```bash
   python setup.py
   ```

2. **Get Freesound API key**:
   - Visit: https://freesound.org/apiv2/apply
   - Add to `.env` file: `FREESOUND_API_KEY=your_key_here`

3. **Generate your first video**:
   ```bash
   python lofi_cli.py generate 120 calming
   ```

## CLI Usage

### Generate Videos
```bash
# Generate 2-minute calming lo-fi video
python lofi_cli.py generate 120 calming

# Generate 5-minute upbeat video with custom filename
python lofi_cli.py generate 300 upbeat -o my_lofi_video.mp4

# Generate 3-minute study music video
python lofi_cli.py generate 180 study
```

### Explore Styles
```bash
# List all available styles
python lofi_cli.py list-styles

# Preview tracks for a specific style
python lofi_cli.py preview chill

# Preview more tracks
python lofi_cli.py preview jazzy -c 10
```

### Examples
```bash
# Quick 2-minute chill video
lofi_cli.py generate 120 chill

# Long-form focus music (10 minutes)
lofi_cli.py generate 600 focus

# Dreamy ambient video
lofi_cli.py generate 240 dreamy -o dreamy_vibes.mp4
```

## Requirements

- **Python 3.9+**
- **FFmpeg** (for video generation)
- **Freesound API Key** (free registration required)

### Installing FFmpeg
```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# macOS
brew install ffmpeg

# Windows
# Download from https://ffmpeg.org/
```

## Video Output

Generated videos include:
- **1920x1080 HD resolution**
- **Style-specific color schemes and animations**
- **Track title and artist overlay**
- **Proper attribution (when required)**
- **MP4 format with H.264 encoding**

## License Compliance

The tool automatically handles license compliance:

- **CC0**: Public domain, no attribution required ✅
- **CC-BY**: Attribution required, commercial use allowed ✅
- **CC-BY-NC**: Attribution required, non-commercial only ⚠️

Attribution is automatically displayed in the CLI output and can be included in video descriptions.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Legal Notice

This application helps you find and use royalty-free music, but you are responsible for:
- Verifying license terms
- Providing proper attribution when required
- Complying with usage restrictions
- Respecting rate limits and terms of service

## Support

For issues and questions:
- Check the documentation
- Review API documentation for each source
- Open an issue on GitHub