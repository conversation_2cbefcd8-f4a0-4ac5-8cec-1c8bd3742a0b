# Read the Docs configuration file
# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

# Required
version: 2

# Set the OS, set of tools, and Python version
build:
  os: ubuntu-22.04
  tools:
    python: "3.9"
  jobs:
    post_create_environment:
      # Install poetry
      - pip install poetry
    post_install:
      # Install dependencies
      - poetry install --with docs

# Build documentation in the docs/ directory with Sphinx
sphinx:
  configuration: docs/conf.py
  fail_on_warning: true

# Optionally build your docs in additional formats such as PDF and ePub
formats:
  - pdf
  - epub

# Optional but recommended, declare the Python requirements required
# to build your documentation
python:
  install:
    - requirements: docs/requirements.txt
    - method: pip
      path: .
      extra_requirements:
        - docs
