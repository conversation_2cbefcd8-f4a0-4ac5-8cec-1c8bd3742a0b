# Lo-Fi Manager API

API reference for the specialized lo-fi music manager.

## Overview

The `LoFiMusicManager` provides a high-level interface for discovering, curating, and managing lo-fi music specifically optimized for video generation.

## LoFiMusicManager

### Class Definition

```python
from src.lofi_manager import LoFiMusicManager

manager = LoFiMusicManager(cache_dir="lofi_cache")
```

### Constructor

**`LoFiMusicManager(cache_dir: str = "lofi_cache")`**

- **`cache_dir`**: Directory for caching audio and metadata

### Configuration

**`configure_sources(freesound_api_key: str, **kwargs)`**

Configure music sources with API credentials.

```python
manager.configure_sources(
    freesound_api_key="your_freesound_api_key"
)
```

## Core Methods

### Track Discovery

**`find_lofi_for_style(style: LoFiStyle, duration: float, limit: int = 20) -> List[Track]`**

Find lo-fi tracks matching a specific style and duration.

```python
from src.music_sources.base import LoFiStyle

tracks = await manager.find_lofi_for_style(
    style=LoFiStyle.CHILL,
    duration=120.0,  # 2 minutes
    limit=10
)

for track in tracks:
    print(f"🎵 {track.title} ({track.duration:.1f}s)")
```

**Parameters:**
- **`style`**: Lo-fi style enum value
- **`duration`**: Target duration in seconds
- **`limit`**: Maximum number of tracks to return

**Returns:** List of `Track` objects sorted by relevance

### Video Track Selection

**`get_track_for_video(duration: float, style: LoFiStyle) -> Optional[Track]`**

Get the best lo-fi track for a video of specified duration and style.

```python
track = await manager.get_track_for_video(
    duration=180.0,  # 3 minutes
    style=LoFiStyle.UPBEAT
)

if track:
    print(f"Selected: {track.title} by {track.artist}")
    print(f"Duration: {track.duration:.1f}s")
    print(f"License: {track.license_type.value}")
```

**Parameters:**
- **`duration`**: Target video duration in seconds
- **`style`**: Desired lo-fi style

**Returns:** Best matching `Track` or `None` if no suitable track found

### Audio Management

**`download_track_audio(track: Track) -> Optional[bytes]`**

Download audio data for a track with caching.

```python
audio_data = await manager.download_track_audio(track)

if audio_data:
    print(f"Downloaded {len(audio_data)} bytes")
    
    # Save to file
    with open("track.mp3", "wb") as f:
        f.write(audio_data)
```

**Returns:** Audio bytes or `None` if download fails

### Playlist Curation

**`curate_style_playlist(style: LoFiStyle, count: int = 50) -> List[Track]`**

Curate a playlist of tracks for a specific style.

```python
playlist = await manager.curate_style_playlist(
    style=LoFiStyle.STUDY,
    count=20
)

print(f"Curated {len(playlist)} study tracks")
for i, track in enumerate(playlist[:5], 1):
    print(f"{i}. {track.title} ({track.duration:.1f}s)")
```

**Parameters:**
- **`style`**: Lo-fi style for playlist
- **`count`**: Number of tracks to curate

**Returns:** List of curated tracks

## Style-Specific Features

### Style Keywords

Each style has associated keywords for better discovery:

```python
# Access style keywords
keywords = manager.STYLE_KEYWORDS[LoFiStyle.CHILL]
print(f"Chill keywords: {keywords}")
# Output: ['chill', 'laid-back', 'relaxed', 'mellow', 'smooth', 'cool']
```

### BPM Ranges

Styles have preferred BPM ranges:

```python
# Access BPM ranges
bpm_range = manager.STYLE_BPM_RANGES[LoFiStyle.UPBEAT]
print(f"Upbeat BPM: {bpm_range[0]}-{bpm_range[1]}")
# Output: Upbeat BPM: 90-120
```

## Filtering and Quality Control

### Lo-Fi Track Filtering

The manager automatically filters tracks to ensure they are authentic lo-fi:

```python
# Internal method (for reference)
def _is_lofi_track(self, track: Track, style: LoFiStyle) -> bool:
    """Check if a track is actually lo-fi music."""
    # Checks for lo-fi indicators in tags and title
    # Excludes non-lo-fi genres
    # Validates duration (30-600 seconds)
    # Returns True if track passes all filters
```

### Style Scoring

Tracks are scored based on how well they match the requested style:

```python
# Internal scoring factors:
# - Style keyword matching in title/tags
# - Duration proximity to target
# - License preference (CC0 > CC-BY > others)
# - Source reliability
```

## License and Attribution

### Attribution Management

**`get_attribution_text(track: Track) -> Optional[str]`**

Get attribution requirements for a track.

```python
attribution = manager.get_attribution_text(track)
if attribution:
    print(f"Attribution required: {attribution[0]}")
else:
    print("No attribution required")
```

### Commercial Use Filtering

The manager prioritizes tracks suitable for commercial use:

```python
# Automatically filters for CC0 and CC-BY licenses
# Avoids CC-BY-NC (non-commercial) when possible
# Clearly indicates license requirements
```

## Caching System

### Cache Management

The manager uses intelligent caching for performance:

```python
# Cache structure:
# lofi_cache/
# ├── metadata/     # Track metadata JSON files
# └── audio/        # Audio preview files

# Cache benefits:
# - Faster subsequent searches
# - Reduced API calls
# - Offline access to downloaded tracks
```

### Cache Operations

```python
# Cache is managed automatically, but you can:

# Clear cache
import shutil
shutil.rmtree("lofi_cache")

# Check cache size
import os
cache_size = sum(
    os.path.getsize(os.path.join(dirpath, filename))
    for dirpath, dirnames, filenames in os.walk("lofi_cache")
    for filename in filenames
)
print(f"Cache size: {cache_size / (1024*1024):.1f} MB")
```

## Static Methods

### Style Utilities

**`get_available_styles() -> List[str]`**

Get list of available lo-fi styles.

```python
styles = LoFiMusicManager.get_available_styles()
print(f"Available styles: {', '.join(styles)}")
# Output: upbeat, calming, chill, dreamy, nostalgic, focus, study, relaxing, ambient, jazzy
```

**`parse_style(style_str: str) -> Optional[LoFiStyle]`**

Parse style string to LoFiStyle enum.

```python
style = LoFiMusicManager.parse_style("chill")
if style:
    print(f"Parsed style: {style.value}")
else:
    print("Invalid style")
```

## Resource Management

### Cleanup

**`close()`**

Clean up resources and close connections.

```python
# Always close when done
await manager.close()

# Or use context manager pattern
async with LoFiMusicManager() as manager:
    manager.configure_sources("api_key")
    track = await manager.get_track_for_video(120.0, LoFiStyle.CHILL)
    # Automatically closed when exiting context
```

## Example Usage

### Complete Video Track Workflow

```python
import asyncio
from src.lofi_manager import LoFiMusicManager
from src.music_sources.base import LoFiStyle

async def generate_video_track():
    manager = LoFiMusicManager()
    
    try:
        # Configure
        manager.configure_sources("your_freesound_api_key")
        
        # Find track for 2-minute chill video
        track = await manager.get_track_for_video(120.0, LoFiStyle.CHILL)
        
        if not track:
            print("No suitable track found")
            return
        
        print(f"Selected track: {track.title}")
        print(f"Artist: {track.artist}")
        print(f"Duration: {track.duration:.1f}s")
        print(f"License: {track.license_type.value}")
        
        # Download audio
        audio_data = await manager.download_track_audio(track)
        if audio_data:
            print(f"Downloaded {len(audio_data)} bytes")
            
            # Save audio file
            with open("video_audio.mp3", "wb") as f:
                f.write(audio_data)
        
        # Check attribution requirements
        attribution = manager.get_attribution_text(track)
        if attribution:
            print(f"Attribution: {attribution[0]}")
        
    finally:
        await manager.close()

asyncio.run(generate_video_track())
```

### Style Exploration

```python
async def explore_styles():
    manager = LoFiMusicManager()
    manager.configure_sources("your_api_key")
    
    try:
        # Test all styles
        for style_name in manager.get_available_styles():
            style = manager.parse_style(style_name)
            tracks = await manager.find_lofi_for_style(style, 180.0, limit=3)
            
            print(f"\n🎨 {style_name.upper()} ({len(tracks)} tracks)")
            for track in tracks:
                print(f"  • {track.title} ({track.duration:.1f}s)")
    
    finally:
        await manager.close()
```

### Playlist Generation

```python
async def create_study_playlist():
    manager = LoFiMusicManager()
    manager.configure_sources("your_api_key")
    
    try:
        # Create 1-hour study playlist
        playlist = await manager.curate_style_playlist(LoFiStyle.STUDY, count=20)
        
        total_duration = sum(track.duration for track in playlist)
        print(f"Study playlist: {len(playlist)} tracks, {total_duration/60:.1f} minutes")
        
        # Download all tracks
        for i, track in enumerate(playlist, 1):
            print(f"Downloading {i}/{len(playlist)}: {track.title}")
            audio_data = await manager.download_track_audio(track)
            if audio_data:
                filename = f"study_{i:02d}_{track.title[:30]}.mp3"
                with open(filename, "wb") as f:
                    f.write(audio_data)
    
    finally:
        await manager.close()
```

## Performance Considerations

### Optimization Tips

1. **Use caching**: Let the manager cache results for better performance
2. **Batch operations**: Process multiple tracks in parallel when possible
3. **Appropriate limits**: Don't request more tracks than needed
4. **Style consistency**: Stick to one style per session for better caching

### Memory Management

```python
# For large batches, consider memory usage
async def process_large_batch():
    manager = LoFiMusicManager()
    
    # Process in chunks to avoid memory issues
    chunk_size = 10
    all_tracks = await manager.curate_style_playlist(LoFiStyle.CHILL, 100)
    
    for i in range(0, len(all_tracks), chunk_size):
        chunk = all_tracks[i:i + chunk_size]
        # Process chunk
        for track in chunk:
            audio_data = await manager.download_track_audio(track)
            # Process audio_data immediately, don't accumulate
```
