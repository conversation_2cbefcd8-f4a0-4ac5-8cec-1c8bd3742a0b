# Music Sources API

API reference for the music sources module.

## Overview

The music sources module provides a unified interface for discovering and downloading royalty-free lo-fi music from various sources.

## Base Classes

### `MusicSource`

Abstract base class for all music source implementations.

```python
from src.music_sources.base import MusicSource

class MusicSource(ABC):
    def __init__(self, api_key: str, **kwargs):
        self.api_key = api_key
        self.session = None
        self._rate_limiter = None
```

#### Methods

**`search(query: SearchQuery) -> SearchResult`**
- Search for tracks matching the given query
- Returns: `SearchResult` with list of tracks
- Raises: `MusicSourceError`, `RateLimitError`, `AuthenticationError`

**`get_track(track_id: str) -> Optional[Track]`**
- Get detailed information about a specific track
- Returns: `Track` object or `None` if not found

**`download_preview(track: Track) -> Optional[bytes]`**
- Download preview audio data for a track
- Returns: Audio bytes or `None` if unavailable

**`download_full(track: Track) -> Optional[bytes]`**
- Download full quality audio data for a track
- Returns: Audio bytes or `None` if unavailable

### `Track`

Represents a music track from any source.

```python
@dataclass
class Track:
    id: str
    title: str
    artist: str
    duration: float  # in seconds
    preview_url: Optional[str] = None
    download_url: Optional[str] = None
    license_type: LicenseType = LicenseType.CUSTOM
    attribution_text: Optional[str] = None
    tags: List[str] = None
    source: str = ""
    bpm: Optional[int] = None
    genre: Optional[str] = None
    mood: Optional[str] = None
    file_size: Optional[int] = None
    file_format: Optional[str] = None
```

#### Properties

- **`id`**: Unique identifier from source
- **`title`**: Track title
- **`artist`**: Artist/creator name
- **`duration`**: Track length in seconds
- **`preview_url`**: URL for preview audio
- **`download_url`**: URL for full audio (may require authentication)
- **`license_type`**: License type (CC0, CC-BY, CC-BY-NC, CUSTOM)
- **`attribution_text`**: Required attribution text (if any)
- **`tags`**: List of descriptive tags
- **`source`**: Source platform name
- **`bpm`**: Beats per minute (if available)
- **`genre`**: Music genre
- **`mood`**: Track mood/style
- **`file_size`**: File size in bytes
- **`file_format`**: Audio format (mp3, wav, etc.)

### `SearchQuery`

Parameters for searching tracks.

```python
@dataclass
class SearchQuery:
    query: str = ""
    style: Optional[LoFiStyle] = None
    min_duration: Optional[float] = None
    max_duration: Optional[float] = None
    license_types: List[LicenseType] = None
    tags: List[str] = None
    limit: int = 20
    offset: int = 0
    lofi_only: bool = True
```

#### Parameters

- **`query`**: Text search query
- **`style`**: Lo-fi style preference
- **`min_duration`**: Minimum track duration in seconds
- **`max_duration`**: Maximum track duration in seconds
- **`license_types`**: Allowed license types
- **`tags`**: Required tags
- **`limit`**: Maximum number of results
- **`offset`**: Result offset for pagination
- **`lofi_only`**: Whether to enforce lo-fi filtering

### `SearchResult`

Result from a track search.

```python
@dataclass
class SearchResult:
    tracks: List[Track]
    total_count: int
    has_more: bool
    next_offset: Optional[int] = None
```

## Enums

### `LicenseType`

Supported license types for tracks.

```python
class LicenseType(Enum):
    CC0 = "cc0"              # Public domain
    CC_BY = "cc_by"          # Attribution required
    CC_BY_NC = "cc_by_nc"    # Attribution required, non-commercial
    CUSTOM = "custom"        # Custom license terms
```

### `LoFiStyle`

Lo-fi music styles/moods.

```python
class LoFiStyle(Enum):
    UPBEAT = "upbeat"
    CALMING = "calming"
    CHILL = "chill"
    DREAMY = "dreamy"
    NOSTALGIC = "nostalgic"
    FOCUS = "focus"
    STUDY = "study"
    RELAXING = "relaxing"
    AMBIENT = "ambient"
    JAZZY = "jazzy"
```

## Freesound Client

### `FreesoundClient`

Client for Freesound.org API v2.

```python
from src.music_sources.freesound import FreesoundClient

client = FreesoundClient(api_key="your_api_key")
```

#### Configuration

- **Base URL**: `https://freesound.org/apiv2`
- **Rate Limits**: 60 requests/minute, 2000/day
- **Authentication**: API key required

#### Methods

**`search(query: SearchQuery) -> SearchResult`**

Search for lo-fi tracks on Freesound.

```python
query = SearchQuery(
    query="chill lofi",
    style=LoFiStyle.CHILL,
    min_duration=60.0,
    max_duration=300.0,
    license_types=[LicenseType.CC0, LicenseType.CC_BY],
    limit=10
)

result = await client.search(query)
print(f"Found {len(result.tracks)} tracks")
```

**`get_track(track_id: str) -> Optional[Track]`**

Get detailed track information.

```python
track = await client.get_track("123456")
if track:
    print(f"Track: {track.title} by {track.artist}")
```

**`download_preview(track: Track) -> Optional[bytes]`**

Download preview audio.

```python
audio_data = await client.download_preview(track)
if audio_data:
    with open("preview.mp3", "wb") as f:
        f.write(audio_data)
```

## Utility Functions

### `get_attribution_text(track: Track) -> Optional[str]`

Generate proper attribution text for a track.

```python
from src.music_sources.base import get_attribution_text

attribution = get_attribution_text(track)
if attribution:
    print(f"Attribution: {attribution}")
```

### `is_commercial_use_allowed(track: Track) -> bool`

Check if a track can be used commercially.

```python
from src.music_sources.base import is_commercial_use_allowed

if is_commercial_use_allowed(track):
    print("✅ Commercial use allowed")
else:
    print("⚠️ Non-commercial only")
```

## Error Handling

### Exception Hierarchy

```python
MusicSourceError              # Base exception
├── RateLimitError           # Rate limit exceeded
└── AuthenticationError      # Invalid credentials
```

### `MusicSourceError`

Base exception for music source operations.

```python
try:
    result = await client.search(query)
except MusicSourceError as e:
    print(f"Search failed: {e}")
```

### `RateLimitError`

Raised when API rate limit is exceeded.

```python
try:
    result = await client.search(query)
except RateLimitError as e:
    print(f"Rate limited. Retry after: {e.retry_after} seconds")
    await asyncio.sleep(e.retry_after or 60)
```

### `AuthenticationError`

Raised when authentication fails.

```python
try:
    result = await client.search(query)
except AuthenticationError:
    print("Invalid API key")
```

## Rate Limiting

### `RateLimiter`

Built-in rate limiter for API requests.

```python
from src.music_sources.base import RateLimiter

limiter = RateLimiter(requests_per_minute=60)
await limiter.acquire()  # Wait if necessary
```

## Example Usage

### Basic Search

```python
import asyncio
from src.music_sources.freesound import FreesoundClient
from src.music_sources.base import SearchQuery, LoFiStyle, LicenseType

async def search_lofi():
    client = FreesoundClient("your_api_key")
    
    query = SearchQuery(
        query="lofi chill",
        style=LoFiStyle.CHILL,
        license_types=[LicenseType.CC0, LicenseType.CC_BY],
        min_duration=120.0,
        limit=5
    )
    
    try:
        result = await client.search(query)
        
        for track in result.tracks:
            print(f"🎵 {track.title} by {track.artist}")
            print(f"   Duration: {track.duration:.1f}s")
            print(f"   License: {track.license_type.value}")
            print(f"   Tags: {', '.join(track.tags[:3])}")
            print()
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        await client.close()

asyncio.run(search_lofi())
```

### Download and Save

```python
async def download_track():
    client = FreesoundClient("your_api_key")
    
    # Search for a track
    query = SearchQuery(query="lofi", limit=1)
    result = await client.search(query)
    
    if result.tracks:
        track = result.tracks[0]
        
        # Download preview
        audio_data = await client.download_preview(track)
        if audio_data:
            filename = f"{track.artist}_{track.title}.mp3"
            with open(filename, "wb") as f:
                f.write(audio_data)
            print(f"Downloaded: {filename}")
    
    await client.close()
```

### License Compliance

```python
from src.music_sources.base import get_attribution_text, is_commercial_use_allowed

def check_license_compliance(tracks):
    commercial_tracks = []
    attributions = []
    
    for track in tracks:
        # Check commercial use
        if is_commercial_use_allowed(track):
            commercial_tracks.append(track)
        
        # Get attribution requirements
        attribution = get_attribution_text(track)
        if attribution:
            attributions.append(attribution)
    
    print(f"Commercial tracks: {len(commercial_tracks)}/{len(tracks)}")
    
    if attributions:
        print("Attribution required:")
        for attr in attributions:
            print(f"  - {attr}")
```
