# CLI Reference

Complete reference for the Lo-Fi Video Generator command-line interface.

## Overview

The `lofi_cli.py` script provides a simple interface for generating lo-fi music videos.

```bash
python3 lofi_cli.py <command> [options]
```

## Global Options

### Help
```bash
python3 lofi_cli.py --help
python3 lofi_cli.py -h
```
Show general help and available commands.

## Commands

### `generate`

Generate a lo-fi music video with specified duration and style.

#### Syntax
```bash
python3 lofi_cli.py generate <duration> <style> [options]
```

#### Arguments

**`duration`** (required)
- Type: Float (seconds)
- Description: Video duration in seconds
- Examples: `30`, `120`, `300.5`

**`style`** (required)
- Type: String
- Description: Lo-fi style/mood
- Valid values: `upbeat`, `calming`, `chill`, `dreamy`, `nostalgic`, `focus`, `study`, `relaxing`, `ambient`, `jazzy`

#### Options

**`-o, --output`** (optional)
- Type: String
- Description: Custom output filename
- Default: Auto-generated based on track and style
- Example: `-o my_video.mp4`

#### Examples

```bash
# Basic usage
python3 lofi_cli.py generate 120 calming

# With custom output
python3 lofi_cli.py generate 300 upbeat -o energetic_vibes.mp4

# Short clip
python3 lofi_cli.py generate 30 chill

# Long session
python3 lofi_cli.py generate 1800 study -o 30min_study.mp4
```

#### Output

The command will:
1. Search for suitable lo-fi tracks
2. Download audio preview
3. Generate video with style-specific visuals
4. Save to `videos/` directory
5. Display attribution requirements

Example output:
```
🎵 Generating 120.0s lo-fi video with calming style...
==================================================
🔍 Searching for lo-fi music...
✅ Found track: 'Peaceful Moments' by ArtistName
   Duration: 145.2s | License: cc_by
   Source: Freesound
⬇️  Downloading audio...
✅ Downloaded 2688192 bytes of audio data
🎬 Generating video...
✅ Video generated successfully!
📁 Output: videos/Peaceful_Moments_calming_120s.mp4

📜 Attribution required:
   "Peaceful Moments" by ArtistName (https://freesound.org/s/123456/) licensed under CC-BY
📊 File size: 1.2 MB
```

### `list-styles`

Display all available lo-fi styles with descriptions.

#### Syntax
```bash
python3 lofi_cli.py list-styles
```

#### Output
```
🎨 Available Lo-Fi Styles:
==============================
  upbeat       - upbeat, energetic, positive
  calming      - calm, peaceful, serene
  chill        - chill, laid-back, relaxed
  dreamy       - dreamy, ethereal, floating
  nostalgic    - nostalgic, vintage, retro
  focus        - focus, concentration, work
  study        - study, reading, learning
  relaxing     - relaxing, stress-relief, meditation
  ambient      - ambient, atmospheric, soundscape
  jazzy        - jazz, swing, blues
```

### `preview`

Preview available tracks for a specific style without generating video.

#### Syntax
```bash
python3 lofi_cli.py preview <style> [options]
```

#### Arguments

**`style`** (required)
- Type: String
- Description: Lo-fi style to preview
- Valid values: Same as `generate` command

#### Options

**`-c, --count`** (optional)
- Type: Integer
- Description: Number of tracks to show
- Default: 5
- Range: 1-20

#### Examples

```bash
# Preview 5 chill tracks (default)
python3 lofi_cli.py preview chill

# Preview 10 upbeat tracks
python3 lofi_cli.py preview upbeat -c 10

# Preview study music
python3 lofi_cli.py preview study --count 3
```

#### Output
```
🎵 Preview of chill lo-fi tracks:
========================================
1. Chill Lo-Fi Melancholy for Reflection
   Artist: LolaMoore
   Duration: 142.3s
   Tags: lofi, chill, ambient, instrumental, hip-hop

2. Relaxing Chill Beats
   Artist: MusicProducer
   Duration: 178.9s
   Tags: chill, beats, relaxing, study, background
```

## Environment Variables

### Required

**`FREESOUND_API_KEY`**
- Description: Your Freesound.org API key
- Required: Yes
- How to get: Register at https://freesound.org/apiv2/apply

```bash
export FREESOUND_API_KEY='your_api_key_here'
```

### Optional

**`CACHE_DIR`**
- Description: Directory for caching audio and metadata
- Default: `lofi_cache`

**`MAX_CACHE_SIZE_MB`**
- Description: Maximum cache size in megabytes
- Default: 1000

## Configuration File

You can use a `.env` file instead of environment variables:

```bash
# .env file
FREESOUND_API_KEY=your_api_key_here
CACHE_DIR=custom_cache
MAX_CACHE_SIZE_MB=500
```

## Exit Codes

- **0**: Success
- **1**: General error (API issues, file errors, etc.)
- **2**: Invalid arguments
- **130**: Interrupted by user (Ctrl+C)

## Error Handling

### Common Errors

**"No suitable lo-fi tracks found"**
```bash
❌ No suitable lo-fi tracks found
Try a different style or duration
```
- Solution: Try different style or check internet connection

**"FREESOUND_API_KEY not found"**
```bash
❌ Error: FREESOUND_API_KEY not found in environment
Please set your Freesound API key:
export FREESOUND_API_KEY='your_key_here'
```
- Solution: Set API key in environment or .env file

**"FFmpeg not found"**
```bash
❌ Missing dependencies:
  - ffmpeg
Please install FFmpeg
```
- Solution: Install FFmpeg for your system

### Debug Mode

For troubleshooting, you can run debug scripts:

```bash
# Test API connection
python3 debug_api.py

# Test track finding
python3 test_video_gen.py

# Test CLI functionality
python3 test_cli.py
```

## Performance Notes

### Timing
- **Track discovery**: 2-5 seconds
- **Audio download**: 1-3 seconds (1-3MB files)
- **Video generation**: 10-30 seconds (depends on duration)
- **Total time**: ~15-40 seconds per video

### Caching
- Audio previews are cached locally
- Metadata is cached for faster subsequent searches
- Cache location: `lofi_cache/` directory

### Rate Limits
- Freesound API: 60 requests/minute, 2000/day
- Automatic rate limiting and retry logic included

## Advanced Usage

### Batch Generation
```bash
# Generate multiple videos
for style in chill upbeat calming; do
    python3 lofi_cli.py generate 120 $style -o "${style}_2min.mp4"
done
```

### Custom Durations
```bash
# Specific durations for different use cases
python3 lofi_cli.py generate 15 upbeat -o instagram_story.mp4    # Social media
python3 lofi_cli.py generate 1500 focus -o pomodoro.mp4         # 25-min work session
python3 lofi_cli.py generate 3600 ambient -o hour_ambient.mp4   # 1-hour background
```

### Integration with Scripts
```bash
#!/bin/bash
# Generate daily study playlist
DATE=$(date +%Y%m%d)
python3 lofi_cli.py generate 1800 study -o "study_${DATE}.mp4"
python3 lofi_cli.py generate 300 relaxing -o "break_${DATE}.mp4"
```

## API Integration

For programmatic usage, you can import the modules directly:

```python
import asyncio
from src.lofi_manager import LoFiMusicManager
from src.video_generator import LoFiVideoGenerator
from src.music_sources.base import LoFiStyle

async def generate_video():
    manager = LoFiMusicManager()
    manager.configure_sources("your_api_key")
    
    track = await manager.get_track_for_video(120.0, LoFiStyle.CHILL)
    # ... rest of video generation logic
```

See the [API Reference](api/lofi-manager.md) for detailed documentation.
