# Quick Start Guide

Get up and running with the Lo-Fi Video Generator in minutes!

## Prerequisites

Before starting, ensure you have:
- ✅ Python 3.9+ installed
- ✅ FFmpeg installed
- ✅ Freesound API key obtained

If not, see the [Installation Guide](installation.md).

## 1. Setup

### Clone and Install
```bash
git clone https://github.com/your-username/lofi-video-generator.git
cd lofi-video-generator
python3 setup.py
```

### Configure API Key
```bash
# Option 1: Environment variable
export FREESOUND_API_KEY='your_freesound_api_key_here'

# Option 2: Edit .env file
echo 'FREESOUND_API_KEY="your_freesound_api_key_here"' > .env
```

## 2. Generate Your First Video

### Basic Usage
```bash
# Generate a 2-minute calming lo-fi video
python3 lofi_cli.py generate 120 calming
```

This will:
1. 🔍 Search for calming lo-fi tracks
2. ⬇️ Download audio preview
3. 🎬 Generate video with style-specific visuals
4. 📁 Save to `videos/` directory
5. 📜 Display attribution requirements

### Example Output
```
🎵 Generating 120.0s lo-fi video with calming style...
==================================================
🔍 Searching for lo-fi music...
✅ Found track: 'Peaceful Lo-Fi Atmosphere' by ArtistName
   Duration: 145.2s | License: cc_by
   Source: Freesound
⬇️  Downloading audio...
✅ Downloaded 2688192 bytes of audio data
🎬 Generating video...
✅ Video generated successfully!
📁 Output: videos/Peaceful_Lo-Fi_Atmosphere_calming_120s.mp4

📜 Attribution required:
   "Peaceful Lo-Fi Atmosphere" by ArtistName (https://freesound.org/s/123456/) licensed under CC-BY
📊 File size: 1.2 MB
```

## 3. Explore Different Styles

### Try All Styles
```bash
# Energetic and positive
python3 lofi_cli.py generate 60 upbeat

# Laid-back and relaxed  
python3 lofi_cli.py generate 90 chill

# Ethereal and atmospheric
python3 lofi_cli.py generate 180 dreamy

# Concentration music
python3 lofi_cli.py generate 300 focus

# Study background music
python3 lofi_cli.py generate 240 study
```

### Custom Output Names
```bash
# Specify custom filename
python3 lofi_cli.py generate 120 nostalgic -o my_nostalgic_vibes.mp4

# Generate for specific use case
python3 lofi_cli.py generate 600 ambient -o meditation_10min.mp4
```

## 4. Explore CLI Features

### List Available Styles
```bash
python3 lofi_cli.py list-styles
```

Output:
```
🎨 Available Lo-Fi Styles:
==============================
  upbeat       - upbeat, energetic, positive
  calming      - calm, peaceful, serene
  chill        - chill, laid-back, relaxed
  dreamy       - dreamy, ethereal, floating
  nostalgic    - nostalgic, vintage, retro
  focus        - focus, concentration, work
  study        - study, reading, learning
  relaxing     - relaxing, stress-relief, meditation
  ambient      - ambient, atmospheric, soundscape
  jazzy        - jazz, swing, blues
```

### Preview Tracks
```bash
# Preview tracks for a style before generating
python3 lofi_cli.py preview chill -c 5
```

### Get Help
```bash
# General help
python3 lofi_cli.py --help

# Command-specific help
python3 lofi_cli.py generate --help
```

## 5. Common Use Cases

### Content Creation
```bash
# YouTube intro (30 seconds)
python3 lofi_cli.py generate 30 upbeat -o youtube_intro.mp4

# Podcast background (10 minutes)
python3 lofi_cli.py generate 600 ambient -o podcast_bg.mp4

# Social media post (15 seconds)
python3 lofi_cli.py generate 15 chill -o instagram_story.mp4
```

### Study/Work Sessions
```bash
# Pomodoro session (25 minutes)
python3 lofi_cli.py generate 1500 focus -o pomodoro_session.mp4

# Study session (1 hour)
python3 lofi_cli.py generate 3600 study -o study_hour.mp4

# Relaxation break (5 minutes)
python3 lofi_cli.py generate 300 relaxing -o break_time.mp4
```

### Mood-Based Videos
```bash
# Morning motivation
python3 lofi_cli.py generate 180 upbeat -o morning_vibes.mp4

# Evening wind-down
python3 lofi_cli.py generate 240 calming -o evening_chill.mp4

# Creative work
python3 lofi_cli.py generate 420 jazzy -o creative_flow.mp4
```

## 6. Understanding Output

### Video Specifications
- **Resolution**: 1920x1080 (Full HD)
- **Format**: MP4 with H.264 encoding
- **Audio**: High-quality lo-fi music preview
- **Visuals**: Style-specific colors and animations
- **Text**: Track title and artist overlay (2-8 seconds)

### File Organization
```
videos/
├── Track_Name_style_duration.mp4
├── my_custom_name.mp4
└── ...

lofi_cache/
├── metadata/
└── audio/
```

### License Information
The CLI automatically displays license requirements:

- **CC0**: ✅ No attribution needed (public domain)
- **CC-BY**: ⚠️ Attribution required (shown in output)
- **CC-BY-NC**: ⚠️ Non-commercial use only + attribution

## 7. Tips for Best Results

### Duration Recommendations
- **Short clips**: 15-60 seconds (social media)
- **Medium videos**: 2-5 minutes (YouTube content)
- **Long sessions**: 10+ minutes (study/work)

### Style Selection
- **upbeat**: Morning content, workout videos
- **calming**: Meditation, sleep content
- **chill**: General background, casual content
- **focus/study**: Productivity, learning content
- **ambient**: Atmospheric, cinematic content

### Performance Tips
- Videos are cached locally for faster regeneration
- Longer videos take more time to generate
- First run downloads and caches tracks

## 8. Next Steps

Now that you're generating videos, explore:

- [CLI Reference](cli-reference.md) - Complete command documentation
- [Styles Guide](styles-guide.md) - Detailed style descriptions
- [Troubleshooting](troubleshooting.md) - Common issues and solutions

## Quick Reference

```bash
# Essential commands
python3 lofi_cli.py list-styles                    # Show all styles
python3 lofi_cli.py generate <duration> <style>    # Generate video
python3 lofi_cli.py preview <style>                # Preview tracks
python3 lofi_cli.py --help                         # Get help

# Examples
python3 lofi_cli.py generate 120 chill             # 2-min chill video
python3 lofi_cli.py generate 300 upbeat -o my.mp4  # Custom filename
```

Happy creating! 🎵✨
