# Contributing

Thank you for your interest in contributing to the Lo-Fi Video Generator! This guide will help you get started.

## Development Setup

### Prerequisites

- Python 3.9+
- FFmpeg
- Git
- Virtual environment tool (venv, conda, etc.)

### Setup Development Environment

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/lofi-video-generator.git
   cd lofi-video-generator
   ```

2. **Create Virtual Environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Development Dependencies**
   ```bash
   pip install -e ".[dev]"
   ```

4. **Setup Pre-commit Hooks**
   ```bash
   pre-commit install
   ```

5. **Configure Environment**
   ```bash
   cp .env.example .env
   # Add your Freesound API key to .env
   ```

### Development Dependencies

- `pytest>=7.0.0` - Testing framework
- `pytest-asyncio>=0.21.0` - Async testing support
- `black>=23.0.0` - Code formatting
- `flake8>=6.0.0` - Linting
- `mypy>=1.0.0` - Type checking
- `pre-commit>=3.0.0` - Git hooks

## Code Style

### Formatting

We use Black for code formatting:

```bash
# Format all code
black src/ tests/ *.py

# Check formatting
black --check src/ tests/ *.py
```

### Linting

We use flake8 for linting:

```bash
# Run linting
flake8 src/ tests/ *.py

# Configuration in setup.cfg
```

### Type Checking

We use mypy for type checking:

```bash
# Run type checking
mypy src/

# Configuration in mypy.ini
```

## Testing

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src

# Run specific test file
pytest tests/test_music_sources.py

# Run specific test
pytest tests/test_music_sources.py::test_freesound_search
```

### Writing Tests

#### Test Structure

```
tests/
├── __init__.py
├── conftest.py              # Shared fixtures
├── test_music_sources.py    # Music source tests
├── test_lofi_manager.py     # Manager tests
├── test_video_generator.py  # Video generation tests
└── test_cli.py             # CLI tests
```

#### Example Test

```python
import pytest
import asyncio
from src.music_sources.freesound import FreesoundClient
from src.music_sources.base import SearchQuery, LoFiStyle

@pytest.mark.asyncio
async def test_freesound_search():
    """Test Freesound search functionality."""
    client = FreesoundClient("test_api_key")
    
    query = SearchQuery(
        query="lofi",
        style=LoFiStyle.CHILL,
        limit=5
    )
    
    # Mock the API response
    with patch.object(client, '_make_request') as mock_request:
        mock_request.return_value = {
            'count': 100,
            'results': [
                {
                    'id': 123,
                    'name': 'Test Track',
                    'username': 'TestUser',
                    'duration': 120.0,
                    'tags': ['lofi', 'chill'],
                    'license': 'https://creativecommons.org/licenses/by/4.0/'
                }
            ]
        }
        
        result = await client.search(query)
        
        assert len(result.tracks) == 1
        assert result.tracks[0].title == 'Test Track'
        assert result.tracks[0].license_type == LicenseType.CC_BY
    
    await client.close()
```

#### Fixtures

```python
# conftest.py
import pytest
from src.lofi_manager import LoFiMusicManager

@pytest.fixture
async def lofi_manager():
    """Create a LoFiMusicManager for testing."""
    manager = LoFiMusicManager(cache_dir="test_cache")
    yield manager
    await manager.close()

@pytest.fixture
def sample_track():
    """Create a sample track for testing."""
    return Track(
        id="test_123",
        title="Test Lo-Fi Track",
        artist="Test Artist",
        duration=120.0,
        license_type=LicenseType.CC0,
        source="Test"
    )
```

## Documentation

### Building Documentation

```bash
# Install documentation dependencies
pip install sphinx sphinx-rtd-theme myst-parser

# Build documentation
cd docs
make html

# View documentation
open _build/html/index.html
```

### Writing Documentation

- Use Markdown for most documentation
- Use reStructuredText for complex formatting
- Include code examples
- Update API documentation when changing code

### Documentation Structure

```
docs/
├── conf.py              # Sphinx configuration
├── index.rst            # Main documentation page
├── installation.md      # Installation guide
├── quickstart.md        # Quick start guide
├── cli-reference.md     # CLI documentation
├── styles-guide.md      # Style descriptions
├── troubleshooting.md   # Common issues
├── api/                 # API reference
│   ├── music-sources.md
│   ├── lofi-manager.md
│   └── video-generator.md
└── _static/            # Static assets
```

## Pull Request Process

### Before Submitting

1. **Run Tests**
   ```bash
   pytest
   ```

2. **Check Code Quality**
   ```bash
   black --check .
   flake8 .
   mypy src/
   ```

3. **Update Documentation**
   - Update relevant documentation
   - Add docstrings to new functions
   - Update CHANGELOG.md

4. **Test Manually**
   ```bash
   python3 lofi_cli.py generate 30 chill
   ```

### Pull Request Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass
- [ ] Manual testing completed
- [ ] Documentation updated

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Comments added for complex code
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
```

## Issue Guidelines

### Bug Reports

Include:
- Python version
- FFmpeg version
- Operating system
- Error messages
- Steps to reproduce
- Expected vs actual behavior

### Feature Requests

Include:
- Use case description
- Proposed solution
- Alternative solutions considered
- Additional context

### Issue Labels

- `bug` - Something isn't working
- `enhancement` - New feature or request
- `documentation` - Documentation improvements
- `good first issue` - Good for newcomers
- `help wanted` - Extra attention needed

## Code Architecture

### Module Structure

```
src/
├── music_sources/       # Music source integrations
│   ├── base.py         # Base classes and types
│   ├── freesound.py    # Freesound.org client
│   └── manager.py      # Source coordination
├── lofi_manager.py     # High-level lo-fi management
└── video_generator.py  # Video generation
```

### Design Principles

1. **Modularity**: Each component has a single responsibility
2. **Extensibility**: Easy to add new music sources
3. **Async/Await**: Non-blocking operations for better performance
4. **Error Handling**: Graceful degradation and clear error messages
5. **Caching**: Intelligent caching for performance
6. **Type Safety**: Type hints throughout the codebase

### Adding New Music Sources

1. **Create Source Class**
   ```python
   from src.music_sources.base import MusicSource
   
   class NewMusicSource(MusicSource):
       @property
       def source_name(self) -> str:
           return "NewSource"
       
       async def search(self, query: SearchQuery) -> SearchResult:
           # Implement search logic
           pass
   ```

2. **Add to Manager**
   ```python
   # In LoFiMusicManager
   def configure_new_source(self, api_key: str):
       source = NewMusicSource(api_key)
       self.add_source(source)
   ```

3. **Add Tests**
   ```python
   # tests/test_new_source.py
   @pytest.mark.asyncio
   async def test_new_source_search():
       # Test implementation
       pass
   ```

## Release Process

### Version Numbering

We use Semantic Versioning (SemVer):
- `MAJOR.MINOR.PATCH`
- Major: Breaking changes
- Minor: New features (backward compatible)
- Patch: Bug fixes

### Release Checklist

1. **Update Version**
   ```python
   # pyproject.toml
   version = "0.2.0"
   ```

2. **Update Changelog**
   ```markdown
   ## [0.2.0] - 2025-01-15
   ### Added
   - New music source integration
   ### Fixed
   - Bug in video generation
   ```

3. **Create Release**
   ```bash
   git tag v0.2.0
   git push origin v0.2.0
   ```

4. **Build and Upload**
   ```bash
   python -m build
   twine upload dist/*
   ```

## Community Guidelines

### Code of Conduct

- Be respectful and inclusive
- Focus on constructive feedback
- Help newcomers learn
- Maintain a welcoming environment

### Communication

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: Questions and general discussion
- **Pull Requests**: Code contributions

### Recognition

Contributors are recognized in:
- CONTRIBUTORS.md file
- Release notes
- Documentation credits

## Getting Help

### Resources

- [Documentation](https://lofi-video-generator.readthedocs.io/)
- [GitHub Issues](https://github.com/lofi-channel/lofi-video-generator/issues)
- [GitHub Discussions](https://github.com/lofi-channel/lofi-video-generator/discussions)

### Mentorship

New contributors can:
- Look for `good first issue` labels
- Ask questions in discussions
- Request code reviews
- Pair program with maintainers

Thank you for contributing to the Lo-Fi Video Generator! 🎵✨
