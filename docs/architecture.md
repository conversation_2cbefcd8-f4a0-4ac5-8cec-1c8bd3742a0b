# Architecture

Technical architecture and design decisions for the Lo-Fi Video Generator.

## System Overview

The Lo-Fi Video Generator is designed as a modular, extensible system for discovering royalty-free lo-fi music and generating videos with style-specific visuals.

```mermaid
graph TB
    CLI[CLI Interface] --> LM[LoFi Manager]
    LM --> MS[Music Sources]
    LM --> VG[Video Generator]
    MS --> FS[Freesound Client]
    MS --> Cache[Cache System]
    VG --> FFmpeg[FFmpeg]
    
    subgraph "Music Sources"
        FS --> API[Freesound API]
        Cache --> Disk[Local Storage]
    end
    
    subgraph "Video Generation"
        FFmpeg --> Visual[Visual Generation]
        FFmpeg --> Audio[Audio Processing]
        FFmpeg --> Combine[Audio/Visual Combine]
    end
```

## Core Components

### 1. CLI Interface (`lofi_cli.py`)

**Purpose**: User-facing command-line interface

**Responsibilities**:
- Parse command-line arguments
- Validate user input
- Coordinate between components
- Display progress and results
- Handle errors gracefully

**Design Patterns**:
- Command pattern for different CLI commands
- Factory pattern for creating managers
- Error handling with user-friendly messages

```python
class LoFiCLI:
    async def generate_video(self, duration: float, style: str, output: str):
        # 1. Validate inputs
        # 2. Initialize managers
        # 3. Find suitable track
        # 4. Generate video
        # 5. Display results
```

### 2. LoFi Manager (`src/lofi_manager.py`)

**Purpose**: High-level orchestration of lo-fi music discovery and curation

**Responsibilities**:
- Style-specific track discovery
- Quality filtering for lo-fi characteristics
- Track scoring and ranking
- Playlist curation
- License compliance management

**Key Features**:
- Style-based keyword mapping
- BPM range preferences
- Intelligent fallback strategies
- Commercial use filtering

```python
class LoFiMusicManager:
    STYLE_KEYWORDS = {...}  # Style-specific search terms
    STYLE_BPM_RANGES = {...}  # BPM preferences per style
    
    async def find_lofi_for_style(self, style, duration, limit):
        # 1. Build style-specific query
        # 2. Search across sources
        # 3. Filter for lo-fi characteristics
        # 4. Score and rank results
        # 5. Return best matches
```

### 3. Music Sources (`src/music_sources/`)

**Purpose**: Abstracted interface for multiple music sources

**Architecture**: Plugin-based with base classes and implementations

#### Base Classes (`base.py`)

```python
# Core abstractions
class MusicSource(ABC):
    async def search(self, query: SearchQuery) -> SearchResult
    async def get_track(self, track_id: str) -> Optional[Track]
    async def download_preview(self, track: Track) -> Optional[bytes]

# Data models
@dataclass
class Track:
    id: str
    title: str
    artist: str
    duration: float
    license_type: LicenseType
    # ... other metadata

@dataclass  
class SearchQuery:
    query: str
    style: Optional[LoFiStyle]
    min_duration: Optional[float]
    max_duration: Optional[float]
    license_types: List[LicenseType]
    # ... other filters
```

#### Freesound Implementation (`freesound.py`)

```python
class FreesoundClient(MusicSource):
    BASE_URL = "https://freesound.org/apiv2"
    
    async def search(self, query: SearchQuery) -> SearchResult:
        # 1. Build API query with filters
        # 2. Make rate-limited request
        # 3. Parse response to Track objects
        # 4. Return SearchResult
```

**Design Decisions**:
- Rate limiting built into client
- Automatic license parsing from URLs
- Flexible query building with OR logic
- Error handling with specific exceptions

#### Source Manager (`manager.py`)

```python
class MusicSourceManager:
    def __init__(self):
        self.sources: Dict[str, MusicSource] = {}
        self.cache = MusicCache()
    
    async def search_all_sources(self, query) -> Dict[str, SearchResult]:
        # Parallel search across all configured sources
```

**Features**:
- Multi-source coordination
- Intelligent caching
- Deduplication across sources
- Attribution management

### 4. Video Generator (`src/video_generator.py`)

**Purpose**: Generate videos by combining audio with style-specific visuals

**Responsibilities**:
- Style-specific visual theme generation
- Audio-visual synchronization
- FFmpeg integration
- Text overlay generation
- Output file management

```python
class LoFiVideoGenerator:
    STYLE_VISUALS = {...}  # Visual themes per style
    
    async def generate_video(self, track, audio_data, duration, style):
        # 1. Generate visual background
        # 2. Create text overlays
        # 3. Combine audio and visual
        # 4. Encode final video
```

**Visual Pipeline**:
1. **Background Generation**: Style-specific colors and animations
2. **Text Overlay**: Track title and artist information
3. **Audio Integration**: Sync audio with visual timeline
4. **Encoding**: H.264/AAC encoding for web compatibility

### 5. Caching System

**Purpose**: Performance optimization through intelligent caching

**Architecture**: Two-tier caching system

```python
class MusicCache:
    def __init__(self, cache_dir: str):
        self.metadata_dir = cache_dir / "metadata"  # JSON files
        self.audio_dir = cache_dir / "audio"        # Audio files
```

**Caching Strategy**:
- **Metadata**: Track information cached as JSON
- **Audio**: Preview files cached as MP3
- **Key Generation**: Hash of source + track ID
- **Expiration**: LRU-based with size limits

## Design Patterns

### 1. Strategy Pattern

**Usage**: Different music sources with common interface

```python
# Base strategy
class MusicSource(ABC):
    async def search(self, query: SearchQuery) -> SearchResult

# Concrete strategies  
class FreesoundClient(MusicSource): ...
class PixabayClient(MusicSource): ...
class MubertClient(MusicSource): ...
```

### 2. Factory Pattern

**Usage**: Creating appropriate managers and clients

```python
class LoFiMusicManager:
    def configure_sources(self, **api_keys):
        if freesound_key := api_keys.get('freesound'):
            self.add_source(FreesoundClient(freesound_key))
        if pixabay_key := api_keys.get('pixabay'):
            self.add_source(PixabayClient(pixabay_key))
```

### 3. Template Method Pattern

**Usage**: Video generation pipeline

```python
class LoFiVideoGenerator:
    async def generate_video(self, ...):
        # Template method defining the algorithm
        visual_path = await self._generate_visual_background(...)
        if not visual_path:
            visual_path = await self._generate_simple_background(...)
        
        success = await self._combine_audio_visual(...)
        return output_path if success else None
```

### 4. Observer Pattern

**Usage**: Progress reporting and logging

```python
class ProgressReporter:
    def __init__(self):
        self.observers = []
    
    def notify(self, event: str, progress: float):
        for observer in self.observers:
            observer.update(event, progress)
```

## Data Flow

### Video Generation Flow

```mermaid
sequenceDiagram
    participant CLI
    participant LM as LoFi Manager
    participant MS as Music Sources
    participant VG as Video Generator
    participant FS as Freesound
    
    CLI->>LM: get_track_for_video(duration, style)
    LM->>MS: search_all_sources(query)
    MS->>FS: search(query)
    FS-->>MS: SearchResult
    MS-->>LM: tracks
    LM->>LM: filter_lofi_tracks()
    LM->>LM: calculate_style_score()
    LM-->>CLI: best_track
    
    CLI->>LM: download_track_audio(track)
    LM->>MS: get_audio_preview(track)
    MS-->>LM: audio_bytes
    LM-->>CLI: audio_data
    
    CLI->>VG: generate_video(track, audio, duration, style)
    VG->>VG: generate_visual_background()
    VG->>VG: combine_audio_visual()
    VG-->>CLI: video_path
```

### Caching Flow

```mermaid
graph LR
    Request[API Request] --> Cache{Cache Hit?}
    Cache -->|Yes| Return[Return Cached]
    Cache -->|No| API[Call API]
    API --> Store[Store in Cache]
    Store --> Return
```

## Error Handling Strategy

### Exception Hierarchy

```python
MusicSourceError                    # Base exception
├── RateLimitError                 # API rate limits
├── AuthenticationError            # Invalid credentials
├── NetworkError                   # Connection issues
└── ValidationError                # Invalid input data

VideoGenerationError               # Video generation issues
├── FFmpegError                   # FFmpeg execution errors
├── InsufficientSpaceError        # Disk space issues
└── CorruptedDataError            # Invalid audio/video data
```

### Error Recovery

1. **Graceful Degradation**: Fallback to simpler options
2. **Retry Logic**: Exponential backoff for transient errors
3. **User Feedback**: Clear error messages with solutions
4. **Logging**: Detailed logs for debugging

```python
async def robust_search(self, query: SearchQuery) -> SearchResult:
    try:
        return await self._primary_search(query)
    except RateLimitError as e:
        await asyncio.sleep(e.retry_after or 60)
        return await self._fallback_search(query)
    except NetworkError:
        return await self._cached_search(query)
```

## Performance Considerations

### Async/Await Architecture

**Benefits**:
- Non-blocking I/O operations
- Concurrent API requests
- Better resource utilization
- Responsive CLI interface

```python
# Concurrent source searching
async def search_all_sources(self, query):
    tasks = []
    for source in self.sources.values():
        task = asyncio.create_task(source.search(query))
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return self._process_results(results)
```

### Caching Strategy

**Multi-level Caching**:
1. **Memory**: In-process caching for current session
2. **Disk**: Persistent caching across sessions
3. **Network**: CDN caching for popular tracks

**Cache Invalidation**:
- Time-based expiration (24 hours for metadata)
- Size-based LRU eviction
- Manual cache clearing

### Resource Management

**Memory Management**:
- Streaming audio processing
- Lazy loading of large datasets
- Automatic cleanup of temporary files

**Disk Management**:
- Configurable cache size limits
- Automatic cleanup of old files
- Efficient file organization

## Security Considerations

### API Key Management

```python
# Environment variable loading
from dotenv import load_dotenv
load_dotenv()

api_key = os.getenv("FREESOUND_API_KEY")
if not api_key:
    raise AuthenticationError("API key not found")
```

### Input Validation

```python
def validate_duration(duration: float) -> float:
    if not 1 <= duration <= 7200:  # 1 second to 2 hours
        raise ValidationError("Duration must be between 1 and 7200 seconds")
    return duration
```

### File System Security

- Sanitized file names
- Restricted output directories
- Permission checks before file operations

## Extensibility

### Adding New Music Sources

1. **Implement MusicSource interface**
2. **Add to MusicSourceManager**
3. **Update configuration methods**
4. **Add tests and documentation**

### Adding New Video Styles

1. **Add to LoFiStyle enum**
2. **Define visual configuration**
3. **Add style keywords and BPM range**
4. **Update documentation**

### Plugin Architecture

Future consideration for plugin-based extensions:

```python
class PluginManager:
    def load_plugins(self, plugin_dir: str):
        # Dynamic loading of music source plugins
        # Dynamic loading of video effect plugins
```

## Testing Strategy

### Unit Tests
- Individual component testing
- Mock external dependencies
- Test error conditions

### Integration Tests
- End-to-end workflow testing
- Real API integration (with test keys)
- File system operations

### Performance Tests
- Load testing with multiple concurrent requests
- Memory usage monitoring
- Cache performance validation

## Deployment Considerations

### Dependencies
- Python 3.9+ runtime
- FFmpeg system dependency
- Network connectivity for APIs

### Configuration
- Environment-based configuration
- Graceful handling of missing dependencies
- Clear setup instructions

### Monitoring
- Structured logging
- Performance metrics
- Error tracking

This architecture provides a solid foundation for the lo-fi video generator while maintaining flexibility for future enhancements and extensions.
