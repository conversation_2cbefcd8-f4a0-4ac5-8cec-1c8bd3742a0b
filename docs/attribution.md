# Attribution

Proper attribution for Creative Commons and other licensed content used in your lo-fi videos.

## Understanding Attribution

When you use music tracks that require attribution, you must properly credit the original creators. This page explains how to handle attribution correctly.

## License Types and Attribution Requirements

### CC0 (Public Domain)
- **Attribution Required**: ❌ No
- **Commercial Use**: ✅ Yes
- **Modification**: ✅ Yes
- **Example**: "Morning Breeze Lo-Fi" by noel0319

```
✅ No attribution needed - use freely!
```

### CC-BY (Attribution)
- **Attribution Required**: ✅ Yes
- **Commercial Use**: ✅ Yes
- **Modification**: ✅ Yes
- **Example**: "Chill Beats" by MusicProducer

```
Required Attribution:
"Chill Beats" by MusicProducer (https://freesound.org/s/123456/) licensed under CC-BY
```

### CC-BY-NC (Attribution Non-Commercial)
- **Attribution Required**: ✅ Yes
- **Commercial Use**: ❌ No
- **Modification**: ✅ Yes (non-commercial only)
- **Example**: "Study Vibes" by StudentBeats

```
Required Attribution:
"Study Vibes" by StudentBeats (https://freesound.org/s/789012/) licensed under CC-BY-NC

⚠️ Non-commercial use only!
```

## Automatic Attribution Generation

The Lo-Fi Video Generator automatically generates proper attribution text:

### CLI Output
When you generate a video, attribution requirements are displayed:

```bash
🎵 Generating 120.0s lo-fi video with chill style...
✅ Video generated successfully!
📁 Output: videos/Chill_Beats_chill_120s.mp4

📜 Attribution required:
   "Chill Beats" by MusicProducer (https://freesound.org/s/123456/) licensed under CC-BY
```

### API Usage
```python
from src.music_sources.base import get_attribution_text

attribution = get_attribution_text(track)
if attribution:
    print(f"Attribution: {attribution}")
```

## Where to Include Attribution

### Video Descriptions
**YouTube, Vimeo, etc.**
```
Music:
"Chill Lo-Fi Beats" by ArtistName (https://freesound.org/s/123456/) licensed under CC-BY

Generated with Lo-Fi Video Generator
```

### Social Media Posts
**Instagram, TikTok, Twitter**
```
🎵 Music: "Chill Beats" by @ArtistName (CC-BY)
🔗 freesound.org/s/123456
#lofi #chillbeats #royaltyfree
```

### Website/Blog Posts
```html
<p>Music: 
<a href="https://freesound.org/s/123456/">"Chill Beats"</a> 
by ArtistName, licensed under 
<a href="https://creativecommons.org/licenses/by/4.0/">CC-BY 4.0</a>
</p>
```

### Video Credits
Include in end credits or description:
```
MUSIC CREDITS
"Peaceful Moments" by RelaxingBeats
Licensed under Creative Commons Attribution (CC-BY)
Source: https://freesound.org/s/123456/
```

## Attribution Best Practices

### Complete Attribution
Always include:
1. **Track Title** (in quotes)
2. **Artist Name** (with "by")
3. **Source URL** (direct link to track)
4. **License Type** (CC-BY, CC-BY-NC, etc.)

### Correct Format
```
✅ Good:
"Chill Vibes" by LoFiProducer (https://freesound.org/s/123456/) licensed under CC-BY

❌ Incomplete:
Music by LoFiProducer

❌ Wrong:
"Chill Vibes" - CC-BY
```

### Multiple Tracks
When using multiple tracks:
```
Music Credits:
1. "Intro Beats" by StartupSounds (https://freesound.org/s/111111/) licensed under CC0
2. "Main Theme" by MainArtist (https://freesound.org/s/222222/) licensed under CC-BY
3. "Outro Chill" by EndingVibes (https://freesound.org/s/333333/) licensed under CC-BY-NC
```

## Platform-Specific Guidelines

### YouTube
- Include attribution in video description
- Pin a comment with music credits
- Use YouTube's built-in attribution features when available

```
📝 MUSIC CREDITS 📝
"Chill Study Beats" by StudyMusicPro
🔗 https://freesound.org/s/123456/
📄 Licensed under CC-BY 4.0
🎵 Generated with Lo-Fi Video Generator
```

### Instagram
- Include attribution in post caption
- Use story highlights for music credits
- Tag artists when possible

```
🎵 Featuring "Dreamy Lo-Fi" by @dreamybeats
Licensed under CC-BY | Link in bio
#lofimusic #royaltyfree #attribution
```

### TikTok
- Include attribution in video description
- Use text overlay for credits
- Link to original source in bio

### Podcasts
- Include attribution in episode description
- Mention credits verbally in episode
- Maintain a credits page on website

## Commercial Use Considerations

### Allowed for Commercial Use
- **CC0**: No attribution needed, full commercial rights
- **CC-BY**: Attribution required, full commercial rights

### NOT Allowed for Commercial Use
- **CC-BY-NC**: Attribution required, non-commercial only

### Determining Commercial Use
**Commercial Use Includes**:
- Monetized YouTube videos
- Paid advertisements
- Business presentations
- Products for sale
- Sponsored content

**Non-Commercial Use Includes**:
- Personal projects
- Educational content (non-profit)
- Non-monetized social media
- Academic presentations

## Tools and Resources

### Attribution Generators
The Lo-Fi Video Generator provides:
```python
# Get attribution text
attribution = manager.get_attribution_text(track)

# Check commercial use
is_commercial = is_commercial_use_allowed(track)

# Get all attributions for multiple tracks
attributions = manager.get_attribution_requirements(tracks)
```

### License Information
```python
# Check license type
if track.license_type == LicenseType.CC0:
    print("No attribution required")
elif track.license_type == LicenseType.CC_BY:
    print("Attribution required, commercial use OK")
elif track.license_type == LicenseType.CC_BY_NC:
    print("Attribution required, non-commercial only")
```

### Batch Attribution
For multiple videos:
```bash
# Generate attribution file
python3 lofi_cli.py generate 120 chill --save-attribution credits.txt
```

## Common Mistakes to Avoid

### ❌ Incomplete Attribution
```
# Missing source URL
"Chill Beats" by ArtistName - CC-BY

# Missing license type  
"Chill Beats" by ArtistName (https://freesound.org/s/123456/)

# Missing artist
"Chill Beats" licensed under CC-BY
```

### ❌ Wrong License Usage
```
# Using CC-BY-NC for commercial content
Using non-commercial track in monetized YouTube video

# Not providing required attribution
Using CC-BY track without crediting artist
```

### ❌ Incorrect Format
```
# Informal attribution
Music from some guy on Freesound

# Broken links
"Track" by Artist (freesound.com/broken-link)
```

## Legal Considerations

### Your Responsibilities
1. **Verify Licenses**: Always check current license terms
2. **Provide Attribution**: Include complete, accurate attribution
3. **Respect Restrictions**: Don't use NC tracks commercially
4. **Keep Records**: Maintain attribution records for all content

### Platform Compliance
- Follow platform-specific attribution guidelines
- Respect platform terms of service
- Update attribution if platform requirements change

### International Considerations
- Attribution requirements may vary by country
- Some platforms have specific local requirements
- Consult legal advice for complex international use

## Getting Help

### Attribution Questions
- 📧 Email: <EMAIL>
- 🐛 GitHub Issues: Report attribution bugs
- 📖 Documentation: Check latest guidelines

### License Verification
- 🔗 Freesound: Check original track page
- 📄 Creative Commons: Official license texts
- ⚖️ Legal Advice: Consult lawyer for complex cases

## Examples and Templates

### Video Description Template
```
🎵 MUSIC CREDITS 🎵

Track: "[TRACK_TITLE]"
Artist: [ARTIST_NAME]
Source: [FREESOUND_URL]
License: [LICENSE_TYPE]

Generated with Lo-Fi Video Generator
https://github.com/lofi-channel/lofi-video-generator

#lofi #royaltyfree #[STYLE]
```

### Social Media Template
```
🎵 "[TRACK_TITLE]" by [ARTIST_NAME]
📄 Licensed under [LICENSE_TYPE]
🔗 [SOURCE_URL]
🎬 Made with Lo-Fi Video Generator
#lofimusic #attribution #royaltyfree
```

### Website Footer Template
```html
<div class="music-attribution">
    <h3>Music Credits</h3>
    <p>
        <strong>"[TRACK_TITLE]"</strong> by 
        <a href="[ARTIST_PROFILE]">[ARTIST_NAME]</a><br>
        Source: <a href="[TRACK_URL]">[TRACK_URL]</a><br>
        Licensed under <a href="[LICENSE_URL]">[LICENSE_TYPE]</a>
    </p>
</div>
```

Remember: Proper attribution respects creators' rights and ensures you can legally use their work. When in doubt, provide more attribution rather than less!
