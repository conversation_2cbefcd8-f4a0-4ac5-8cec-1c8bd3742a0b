# License

## Project License

The Lo-Fi Video Generator is released under the MIT License.

```
MIT License

Copyright (c) 2025 Lo-Fi Channel

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## What This Means

### ✅ You Can
- **Use** the software for any purpose, including commercial use
- **Modify** the source code to fit your needs
- **Distribute** copies of the software
- **Sublicense** the software under different terms
- **Sell** copies or modified versions

### ⚠️ You Must
- **Include** the original copyright notice and license text
- **Include** the license in any substantial portions you use

### ❌ We Don't Provide
- **Warranty** of any kind
- **Liability** for damages or issues
- **Support** guarantees (though we try our best!)

## Third-Party Licenses

This project uses several third-party libraries and services, each with their own licenses:

### Python Dependencies

#### aiohttp
- **License**: Apache License 2.0
- **Usage**: HTTP client for API requests
- **Source**: https://github.com/aio-libs/aiohttp

#### aiofiles  
- **License**: Apache License 2.0
- **Usage**: Async file operations
- **Source**: https://github.com/Tinche/aiofiles

#### python-dotenv
- **License**: BSD 3-Clause License
- **Usage**: Environment variable management
- **Source**: https://github.com/theskumar/python-dotenv

### External Services

#### Freesound.org
- **Service License**: Creative Commons and custom licenses
- **Usage**: Music source for lo-fi tracks
- **Terms**: https://freesound.org/help/tos_web/
- **API Terms**: https://freesound.org/help/developers/

**Important**: Music tracks from Freesound have their own individual licenses (CC0, CC-BY, CC-BY-NC, etc.). You must comply with each track's specific license terms.

#### FFmpeg
- **License**: LGPL 2.1+ / GPL 2+
- **Usage**: Video and audio processing
- **Source**: https://ffmpeg.org/
- **License Details**: https://ffmpeg.org/legal.html

### Documentation

#### Sphinx
- **License**: BSD 2-Clause License
- **Usage**: Documentation generation
- **Source**: https://github.com/sphinx-doc/sphinx

#### Read the Docs Theme
- **License**: MIT License
- **Usage**: Documentation theme
- **Source**: https://github.com/readthedocs/sphinx_rtd_theme

## Music Licensing

### Generated Videos

When you generate videos using this tool, the **software license (MIT)** applies to the tool itself, but **music licenses** apply to the audio content:

#### Track Licenses

**CC0 (Public Domain)**
- ✅ No attribution required
- ✅ Commercial use allowed
- ✅ Modification allowed
- ✅ No restrictions

**CC-BY (Attribution)**
- ⚠️ Attribution required
- ✅ Commercial use allowed
- ✅ Modification allowed
- 📝 Must credit artist and source

**CC-BY-NC (Attribution Non-Commercial)**
- ⚠️ Attribution required
- ❌ Commercial use NOT allowed
- ✅ Modification allowed (for non-commercial use)
- 📝 Must credit artist and source

#### Attribution Format

For tracks requiring attribution, use this format:
```
"Track Title" by Artist Name (https://freesound.org/s/ID/) 
licensed under [License Type]
```

Example:
```
"Chill Lo-Fi Beats" by MusicProducer 
(https://freesound.org/s/123456/) licensed under CC-BY
```

### Your Responsibilities

1. **Check Each Track**: Verify the license of each track you use
2. **Provide Attribution**: Include required attribution in video descriptions
3. **Respect Restrictions**: Don't use CC-BY-NC tracks for commercial purposes
4. **Keep Records**: Maintain records of tracks and their licenses

## Compliance Tools

The Lo-Fi Video Generator helps with license compliance:

### Automatic Detection
- Parses license information from source APIs
- Displays license type for each track
- Generates proper attribution text

### CLI Output
```bash
📜 Attribution required:
   "Peaceful Moments" by ArtistName (https://freesound.org/s/123456/) licensed under CC-BY
```

### API Methods
```python
# Check if commercial use is allowed
is_commercial = is_commercial_use_allowed(track)

# Get attribution text
attribution = get_attribution_text(track)
```

## Disclaimer

### Software Disclaimer

This software is provided "as is" without warranty. While we strive to provide accurate license information, **you are responsible for verifying the license terms** of any music you use.

### Music Disclaimer

- **License Accuracy**: We parse license information from APIs, but terms may change
- **Usage Rights**: Always verify current license terms on the source platform
- **Legal Advice**: This is not legal advice - consult a lawyer for complex licensing questions
- **Platform Terms**: You must also comply with the terms of service of music platforms

## Reporting Issues

### License Violations

If you believe there's a license violation:
1. Contact us <NAME_EMAIL>
2. Provide details about the violation
3. We will investigate and take appropriate action

### Incorrect License Information

If you find incorrect license information:
1. Open an issue on GitHub
2. Provide the track ID and correct license information
3. We will update our parsing logic

## Commercial Use

### Software
The MIT license allows commercial use of the software itself.

### Generated Content
Commercial use of generated videos depends on the music licenses:
- **CC0 tracks**: ✅ Commercial use allowed
- **CC-BY tracks**: ✅ Commercial use allowed (with attribution)
- **CC-BY-NC tracks**: ❌ Commercial use NOT allowed

### Filtering for Commercial Use
```bash
# The tool can filter for commercial-friendly tracks
python3 lofi_cli.py generate 120 chill --commercial-only
```

## Updates to This License

We may update this license information as:
- New music sources are added
- License terms change
- Legal requirements evolve

Check the latest version at: https://lofi-video-generator.readthedocs.io/en/latest/license/

## Contact

For license questions:
- 📧 Email: <EMAIL>
- 🐛 GitHub Issues: https://github.com/lofi-channel/lofi-video-generator/issues
- 📖 Documentation: https://lofi-video-generator.readthedocs.io/

---

**Remember**: This tool helps you find and use royalty-free music, but you are ultimately responsible for ensuring your use complies with all applicable licenses and terms of service.
