# Installation

## Requirements

- **Python 3.9+**
- **FFmpeg** (for video generation)
- **Freesound API Key** (free registration required)

## System Dependencies

### FFmpeg Installation

FFmpeg is required for video generation. Install it using your system's package manager:

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install ffmpeg
```

#### macOS
```bash
# Using Homebrew
brew install ffmpeg

# Using MacPorts
sudo port install ffmpeg
```

#### Windows
1. Download FFmpeg from [https://ffmpeg.org/download.html](https://ffmpeg.org/download.html)
2. Extract to a folder (e.g., `C:\ffmpeg`)
3. Add `C:\ffmpeg\bin` to your system PATH

#### Verify Installation
```bash
ffmpeg -version
```

## Python Dependencies

### Automatic Setup
The easiest way to install all dependencies:

```bash
python3 setup.py
```

This will:
- Install Python dependencies
- Create `.env` file from template
- Check for FFmpeg
- Provide setup instructions

### Manual Installation

#### Using pip
```bash
pip install -e .
```

#### Using pip with development dependencies
```bash
pip install -e ".[dev]"
```

#### Required Dependencies
- `aiohttp>=3.8.0` - HTTP client for API requests
- `aiofiles>=23.0.0` - Async file operations
- `python-dotenv>=1.0.0` - Environment variable management

## API Key Setup

### Get Freesound API Key

1. Visit [https://freesound.org/apiv2/apply](https://freesound.org/apiv2/apply)
2. Create a free account or log in
3. Fill out the API application form
4. Copy your API key

### Configure API Key

#### Option 1: Environment Variable
```bash
export FREESOUND_API_KEY='your_api_key_here'
```

#### Option 2: .env File
Create a `.env` file in the project root:
```bash
echo 'FREESOUND_API_KEY="your_api_key_here"' > .env
```

#### Option 3: Edit .env Template
```bash
cp .env.example .env
# Edit .env file and add your API key
```

## Verification

Test your installation:

```bash
# Check CLI is working
python3 lofi_cli.py --help

# List available styles
python3 lofi_cli.py list-styles

# Test API connection (requires API key)
python3 lofi_cli.py preview chill -c 3
```

## Docker Installation (Optional)

For containerized deployment:

```dockerfile
FROM python:3.9-slim

# Install FFmpeg
RUN apt-get update && apt-get install -y ffmpeg && rm -rf /var/lib/apt/lists/*

# Copy project
COPY . /app
WORKDIR /app

# Install dependencies
RUN pip install -e .

# Set environment
ENV FREESOUND_API_KEY=""

CMD ["python3", "lofi_cli.py", "--help"]
```

Build and run:
```bash
docker build -t lofi-generator .
docker run -e FREESOUND_API_KEY="your_key" lofi-generator generate 120 chill
```

## Troubleshooting

### Common Issues

#### "FFmpeg not found"
- Ensure FFmpeg is installed and in your PATH
- Try running `ffmpeg -version` to verify

#### "API key error"
- Verify your API key is correct
- Check that it's properly set in environment or .env file
- Ensure you have internet connection

#### "Import errors"
- Run from the project root directory
- Ensure all dependencies are installed: `pip install -e .`

#### "Permission denied"
- Make sure you have write permissions in the project directory
- Check that `videos/` directory can be created

### Getting Help

If you encounter issues:

1. Check the [Troubleshooting Guide](troubleshooting.md)
2. Verify all requirements are met
3. Run the debug script: `python3 debug_api.py`
4. Open an issue on GitHub with error details

## Next Steps

Once installed, proceed to the [Quick Start Guide](quickstart.md) to generate your first lo-fi video!
