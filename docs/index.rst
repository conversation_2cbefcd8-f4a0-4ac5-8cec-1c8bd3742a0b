Lo-Fi Video Generator Documentation
===================================

.. image:: https://img.shields.io/badge/python-3.9+-blue.svg
   :target: https://www.python.org/downloads/
   :alt: Python Version

.. image:: https://img.shields.io/badge/license-MIT-green.svg
   :target: https://opensource.org/licenses/MIT
   :alt: License

.. image:: https://readthedocs.org/projects/lofi-video-generator/badge/?version=latest
   :target: https://lofi-video-generator.readthedocs.io/en/latest/?badge=latest
   :alt: Documentation Status

A CLI tool for generating lo-fi music videos with royalty-free music and style-specific visuals.

🎵 **Generate beautiful lo-fi videos with a single command!**

.. code-block:: bash

   # Generate 2-minute calming lo-fi video
   python3 lofi_cli.py generate 120 calming

   # Generate 5-minute upbeat video with custom name
   python3 lofi_cli.py generate 300 upbeat -o my_video.mp4

Features
--------

🎵 **Lo-Fi Music Discovery**
   Curated lo-fi tracks from Freesound.org with intelligent filtering

🎬 **Video Generation**
   Automatic video creation with style-specific visuals and animations

🎨 **Multiple Styles**
   10 different lo-fi styles: calming, upbeat, chill, dreamy, nostalgic, focus, study, relaxing, ambient, jazzy

📜 **License Compliance**
   Automatic attribution for Creative Commons tracks

💾 **Smart Caching**
   Local caching for better performance and reduced API calls

⚡ **CLI Interface**
   Simple command-line interface for quick video generation

Quick Start
-----------

1. **Get Freesound API Key** (free):

   Visit https://freesound.org/apiv2/apply and register for a free account.

2. **Install FFmpeg**:

   .. code-block:: bash

      # Ubuntu/Debian
      sudo apt install ffmpeg
      
      # macOS
      brew install ffmpeg

3. **Setup Project**:

   .. code-block:: bash

      python3 setup.py
      # Edit .env file with your API key

4. **Generate Your First Video**:

   .. code-block:: bash

      export FREESOUND_API_KEY='your_key_here'
      python3 lofi_cli.py generate 120 calming

Available Styles
----------------

.. list-table::
   :header-rows: 1
   :widths: 15 85

   * - Style
     - Description
   * - **upbeat**
     - Energetic, positive, bouncy lo-fi beats
   * - **calming**
     - Peaceful, serene, tranquil soundscapes
   * - **chill**
     - Laid-back, relaxed, mellow vibes
   * - **dreamy**
     - Ethereal, floating, atmospheric melodies
   * - **nostalgic**
     - Vintage, retro, warm memories
   * - **focus**
     - Concentration, work, productivity music
   * - **study**
     - Reading, learning, background ambience
   * - **relaxing**
     - Stress-relief, meditation, zen sounds
   * - **ambient**
     - Atmospheric, textural soundscapes
   * - **jazzy**
     - Jazz-influenced, swing, blues elements

Video Output
------------

Generated videos include:

- **1920x1080 HD resolution**
- **Style-specific color schemes and animations**
- **Track title and artist overlay**
- **Proper attribution (when required)**
- **MP4 format with H.264 encoding**

License Compliance
------------------

The tool automatically handles license compliance:

.. note::
   - **CC0**: Public domain, no attribution required ✅
   - **CC-BY**: Attribution required, commercial use allowed ✅
   - **CC-BY-NC**: Attribution required, non-commercial only ⚠️

Attribution text is automatically displayed in the CLI output and should be included in video descriptions when required.

Table of Contents
-----------------

.. toctree::
   :maxdepth: 2
   :caption: User Guide

   installation
   quickstart
   cli-reference
   styles-guide
   troubleshooting
   error-tracking

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/music-sources
   api/video-generator
   api/lofi-manager

.. toctree::
   :maxdepth: 2
   :caption: Development

   contributing
   architecture
   changelog

.. toctree::
   :maxdepth: 1
   :caption: Legal

   license
   attribution

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
