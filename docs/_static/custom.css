/* Custom CSS for Lo-Fi Video Generator documentation */

/* Brand colors */
:root {
    --lofi-primary: #2980B9;
    --lofi-secondary: #8E44AD;
    --lofi-accent: #E74C3C;
    --lofi-success: #27AE60;
    --lofi-warning: #F39C12;
    --lofi-dark: #2C3E50;
    --lofi-light: #ECF0F1;
}

/* Header customization */
.wy-nav-top {
    background: linear-gradient(135deg, var(--lofi-primary), var(--lofi-secondary));
}

/* Sidebar customization */
.wy-nav-side {
    background: var(--lofi-dark);
}

.wy-menu-vertical a {
    color: var(--lofi-light);
}

.wy-menu-vertical a:hover {
    background-color: var(--lofi-primary);
    color: white;
}

/* Content area */
.wy-nav-content {
    background: #fafafa;
}

/* Code blocks */
.highlight {
    background: #f8f8f8;
    border-left: 4px solid var(--lofi-primary);
    padding-left: 1em;
}

/* Admonitions */
.admonition.note {
    border-left: 4px solid var(--lofi-primary);
}

.admonition.warning {
    border-left: 4px solid var(--lofi-warning);
}

.admonition.tip {
    border-left: 4px solid var(--lofi-success);
}

/* Tables */
.wy-table-responsive table td,
.wy-table-responsive table th {
    white-space: normal;
}

/* Custom badges */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-primary {
    color: #fff;
    background-color: var(--lofi-primary);
}

.badge-success {
    color: #fff;
    background-color: var(--lofi-success);
}

.badge-warning {
    color: #212529;
    background-color: var(--lofi-warning);
}

/* Style-specific colors */
.style-upbeat { color: #E91E63; font-weight: bold; }
.style-calming { color: #9C27B0; font-weight: bold; }
.style-chill { color: #2196F3; font-weight: bold; }
.style-dreamy { color: #673AB7; font-weight: bold; }
.style-nostalgic { color: #795548; font-weight: bold; }
.style-focus { color: #607D8B; font-weight: bold; }
.style-study { color: #9E9E9E; font-weight: bold; }
.style-relaxing { color: #4CAF50; font-weight: bold; }
.style-ambient { color: #3F51B5; font-weight: bold; }
.style-jazzy { color: #FF5722; font-weight: bold; }

/* Command examples */
.command-example {
    background: #2C3E50;
    color: #ECF0F1;
    padding: 1em;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    margin: 1em 0;
}

.command-example::before {
    content: "$ ";
    color: var(--lofi-success);
    font-weight: bold;
}

/* License indicators */
.license-cc0 {
    background: var(--lofi-success);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
}

.license-cc-by {
    background: var(--lofi-warning);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
}

.license-cc-by-nc {
    background: var(--lofi-accent);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
}

/* Responsive improvements */
@media screen and (max-width: 768px) {
    .wy-table-responsive table {
        font-size: 0.9em;
    }
    
    .command-example {
        font-size: 0.8em;
        overflow-x: auto;
    }
}

/* Print styles */
@media print {
    .wy-nav-side,
    .wy-nav-top,
    .rst-footer-buttons {
        display: none !important;
    }
    
    .wy-nav-content {
        margin-left: 0 !important;
    }
}
