# Styles Guide

Comprehensive guide to all 10 lo-fi styles available in the video generator.

## Overview

Each style has unique characteristics that influence:
- **Music Selection**: Specific keywords and mood filters
- **Visual Theme**: Color schemes and animation styles  
- **BPM Range**: Tempo preferences for the style
- **Use Cases**: Recommended applications

## Style Descriptions

### 🎉 Upbeat

**Keywords**: `upbeat`, `energetic`, `positive`, `happy`, `bouncy`, `groove`

**Characteristics**:
- BPM Range: 90-120
- Energy Level: High
- Mood: Positive, motivating
- Visual Colors: Bright pastels (#FFB6C1, #87CEEB, #98FB98, #F0E68C)

**Best For**:
- Morning motivation videos
- Workout background music
- Creative project intros
- Social media content
- Productivity sessions

**Example Tracks**:
- "Lo-Fi - Morning Breeze" by noel0319
- "Upbeat Lo-Fi Hip Hop" by various artists
- "Positive Vibes Instrumental" by producers

```bash
# Generate upbeat content
python3 lofi_cli.py generate 60 upbeat -o morning_motivation.mp4
python3 lofi_cli.py generate 180 upbeat -o workout_warmup.mp4
```

---

### 🕊️ Calming

**Keywords**: `calm`, `peaceful`, `serene`, `tranquil`, `soothing`, `gentle`

**Characteristics**:
- BPM Range: 60-80
- Energy Level: Low
- Mood: Peaceful, serene
- Visual Colors: Soft pastels (#E6E6FA, #F0F8FF, #F5F5DC, #FFF8DC)

**Best For**:
- Meditation videos
- Sleep content
- Stress relief sessions
- Evening wind-down
- Therapy/wellness content

**Example Tracks**:
- "Peaceful Lo-Fi Atmosphere for Peaceful Moments" by LolaMoore
- "Calming Ambient Soundscape" by ambient artists
- "Tranquil Beats for Relaxation" by chill producers

```bash
# Generate calming content
python3 lofi_cli.py generate 300 calming -o meditation_5min.mp4
python3 lofi_cli.py generate 600 calming -o sleep_preparation.mp4
```

---

### 😎 Chill

**Keywords**: `chill`, `laid-back`, `relaxed`, `mellow`, `smooth`, `cool`

**Characteristics**:
- BPM Range: 70-90
- Energy Level: Medium-Low
- Mood: Relaxed, casual
- Visual Colors: Cool blues (#4682B4, #708090, #778899, #B0C4DE)

**Best For**:
- General background music
- Casual content creation
- Streaming backgrounds
- Coffee shop ambiance
- Everyday listening

**Example Tracks**:
- "Chill Lo-Fi Melancholy for Reflection" by LolaMoore
- "Smooth Chill Beats" by lo-fi producers
- "Laid-back Hip Hop Instrumental" by beat makers

```bash
# Generate chill content
python3 lofi_cli.py generate 240 chill -o background_vibes.mp4
python3 lofi_cli.py generate 120 chill -o casual_content.mp4
```

---

### ✨ Dreamy

**Keywords**: `dreamy`, `ethereal`, `floating`, `atmospheric`, `spacey`, `soft`

**Characteristics**:
- BPM Range: 50-70
- Energy Level: Low-Medium
- Mood: Ethereal, otherworldly
- Visual Colors: Soft purples (#DDA0DD, #E6E6FA, #F0E68C, #FFB6C1)

**Best For**:
- Artistic content
- Fantasy/sci-fi themes
- Creative inspiration
- Night-time content
- Atmospheric videos

**Example Tracks**:
- "Dreamy Lo-Fi Soundscape" by ambient artists
- "Ethereal Beats for Contemplation" by producers
- "Floating Melodies" by atmospheric creators

```bash
# Generate dreamy content
python3 lofi_cli.py generate 360 dreamy -o artistic_inspiration.mp4
python3 lofi_cli.py generate 180 dreamy -o night_vibes.mp4
```

---

### 📼 Nostalgic

**Keywords**: `nostalgic`, `vintage`, `retro`, `memories`, `warm`, `classic`

**Characteristics**:
- BPM Range: 70-95
- Energy Level: Medium
- Mood: Sentimental, warm
- Visual Colors: Warm browns (#D2B48C, #F5DEB3, #DEB887, #CD853F)

**Best For**:
- Memory/throwback content
- Vintage-themed videos
- Storytelling backgrounds
- Photo slideshow music
- Emotional content

**Example Tracks**:
- "Nostalgic Lo-Fi Memories" by retro producers
- "Vintage Hip Hop Instrumental" by classic beat makers
- "Warm Analog Sounds" by lo-fi artists

```bash
# Generate nostalgic content
python3 lofi_cli.py generate 200 nostalgic -o memory_lane.mp4
python3 lofi_cli.py generate 150 nostalgic -o vintage_vibes.mp4
```

---

### 🎯 Focus

**Keywords**: `focus`, `concentration`, `work`, `productivity`, `minimal`, `steady`

**Characteristics**:
- BPM Range: 80-100
- Energy Level: Medium
- Mood: Concentrated, steady
- Visual Colors: Neutral grays (#2F4F4F, #696969, #708090, #A9A9A9)

**Best For**:
- Work sessions
- Productivity content
- Deep work backgrounds
- Professional videos
- Task-focused activities

**Example Tracks**:
- "Focus Lo-Fi for Deep Work" by productivity artists
- "Concentration Beats" by work music producers
- "Minimal Hip Hop for Focus" by beat makers

```bash
# Generate focus content
python3 lofi_cli.py generate 1500 focus -o pomodoro_25min.mp4
python3 lofi_cli.py generate 3600 focus -o deep_work_hour.mp4
```

---

### 📚 Study

**Keywords**: `study`, `reading`, `learning`, `quiet`, `background`, `subtle`

**Characteristics**:
- BPM Range: 65-85
- Energy Level: Low-Medium
- Mood: Quiet, supportive
- Visual Colors: Soft grays (#F5F5F5, #DCDCDC, #D3D3D3, #C0C0C0)

**Best For**:
- Study sessions
- Reading backgrounds
- Educational content
- Library ambiance
- Learning environments

**Example Tracks**:
- "Study Lo-Fi Beats for Learning" by educational producers
- "Quiet Background Music" by ambient artists
- "Subtle Hip Hop for Reading" by study music creators

```bash
# Generate study content
python3 lofi_cli.py generate 2700 study -o study_45min.mp4
python3 lofi_cli.py generate 1800 study -o reading_session.mp4
```

---

### 🧘 Relaxing

**Keywords**: `relaxing`, `stress-relief`, `meditation`, `zen`, `mindful`, `slow`

**Characteristics**:
- BPM Range: 50-75
- Energy Level: Low
- Mood: Peaceful, healing
- Visual Colors: Nature greens (#98FB98, #90EE90, #8FBC8F, #20B2AA)

**Best For**:
- Meditation sessions
- Yoga videos
- Stress relief content
- Wellness applications
- Mindfulness practices

**Example Tracks**:
- "Relaxing Lo-Fi for Stress Relief" by wellness producers
- "Zen Beats for Meditation" by mindfulness artists
- "Peaceful Instrumental" by relaxation creators

```bash
# Generate relaxing content
python3 lofi_cli.py generate 900 relaxing -o meditation_15min.mp4
python3 lofi_cli.py generate 1200 relaxing -o yoga_session.mp4
```

---

### 🌌 Ambient

**Keywords**: `ambient`, `atmospheric`, `soundscape`, `texture`, `space`, `drone`

**Characteristics**:
- BPM Range: 40-70
- Energy Level: Low
- Mood: Atmospheric, expansive
- Visual Colors: Deep purples (#191970, #483D8B, #6A5ACD, #9370DB)

**Best For**:
- Atmospheric content
- Space/sci-fi themes
- Background soundscapes
- Cinematic videos
- Immersive experiences

**Example Tracks**:
- "Ambient Lo-Fi Soundscape" by atmospheric producers
- "Space-like Textures" by ambient artists
- "Drone-influenced Hip Hop" by experimental creators

```bash
# Generate ambient content
python3 lofi_cli.py generate 600 ambient -o atmospheric_10min.mp4
python3 lofi_cli.py generate 1800 ambient -o cinematic_bg.mp4
```

---

### 🎷 Jazzy

**Keywords**: `jazz`, `swing`, `blues`, `saxophone`, `piano`, `improvisation`

**Characteristics**:
- BPM Range: 80-110
- Energy Level: Medium-High
- Mood: Sophisticated, smooth
- Visual Colors: Warm browns (#8B4513, #A0522D, #CD853F, #D2691E)

**Best For**:
- Sophisticated content
- Coffee shop ambiance
- Jazz-influenced videos
- Classy backgrounds
- Musical content

**Example Tracks**:
- "Jazzy Lo-Fi Hip Hop" by jazz-influenced producers
- "Smooth Jazz Beats" by sophisticated artists
- "Piano-driven Lo-Fi" by jazz creators

```bash
# Generate jazzy content
python3 lofi_cli.py generate 240 jazzy -o coffee_shop_vibes.mp4
python3 lofi_cli.py generate 420 jazzy -o sophisticated_bg.mp4
```

## Style Selection Guide

### By Use Case

| Use Case | Recommended Styles | Duration |
|----------|-------------------|----------|
| **Morning Routine** | upbeat, calming | 60-180s |
| **Work/Productivity** | focus, study | 1500-3600s |
| **Creative Work** | jazzy, dreamy, chill | 300-1800s |
| **Exercise** | upbeat, chill | 180-600s |
| **Relaxation** | calming, relaxing, ambient | 300-1800s |
| **Study Sessions** | study, focus | 1800-5400s |
| **Social Media** | upbeat, chill, dreamy | 15-60s |
| **Background Music** | ambient, chill, study | 600-3600s |

### By Time of Day

| Time | Morning | Afternoon | Evening | Night |
|------|---------|-----------|---------|-------|
| **Styles** | upbeat, focus | chill, jazzy | calming, nostalgic | dreamy, ambient, relaxing |

### By Energy Level

| Energy | High | Medium | Low |
|--------|------|--------|-----|
| **Styles** | upbeat | focus, jazzy, nostalgic, chill | calming, study, relaxing, dreamy, ambient |

## Visual Themes

Each style has a unique visual identity:

### Color Psychology
- **Warm colors** (nostalgic, jazzy): Comfort, sophistication
- **Cool colors** (chill, ambient): Calm, spacious
- **Bright colors** (upbeat): Energy, positivity
- **Neutral colors** (focus, study): Minimal distraction
- **Soft colors** (calming, dreamy): Peace, tranquility

### Animation Styles
- **Bouncy** (upbeat): Dynamic movement
- **Smooth** (chill, jazzy): Flowing transitions
- **Gentle** (calming, relaxing): Slow, peaceful motion
- **Steady** (focus, study): Minimal, consistent
- **Floating** (dreamy, ambient): Ethereal movement

## Tips for Best Results

### Duration Guidelines
- **Short clips** (15-60s): upbeat, chill
- **Medium videos** (2-5 min): All styles work well
- **Long sessions** (10+ min): focus, study, ambient, relaxing

### Style Combinations
For longer content, consider mixing styles:
```bash
# Create a study playlist
python3 lofi_cli.py generate 1800 focus -o focus_30min.mp4
python3 lofi_cli.py generate 300 relaxing -o break_5min.mp4
python3 lofi_cli.py generate 1800 study -o study_30min.mp4
```

### Quality Considerations
- All styles prioritize high-quality lo-fi tracks
- Automatic filtering ensures authentic lo-fi characteristics
- License compliance is handled automatically
- Attribution requirements are clearly displayed
