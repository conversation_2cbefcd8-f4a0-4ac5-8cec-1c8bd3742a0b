# Error Tracking & Monitoring

Comprehensive error tracking and performance monitoring for the Lo-Fi Video Generator using Sentry and GitLab integration.

## Overview

The Lo-Fi Video Generator includes built-in error tracking that automatically captures and reports:
- **Exceptions and Errors**: Unhandled errors with full stack traces
- **Performance Metrics**: Video generation timing and bottlenecks
- **User Context**: System information and operation details
- **Breadcrumbs**: Step-by-step operation tracking for debugging

## Configuration

### Environment Setup

Add to your `.env` file:
```bash
# GitLab Error Tracking Integration
SENTRY_DSN=https://<EMAIL>:443/errortracking/api/v1/projects/71280014
SENTRY_ENVIRONMENT=production
SENTRY_RELEASE=0.1.0
ENABLE_ERROR_TRACKING=true
```

### Environment Types

The system automatically detects environments:
- **development**: Local development
- **production**: Production deployment
- **ci**: Continuous integration
- **testing**: Test runs (errors not sent)

### Manual Configuration

```python
from src.sentry_config import configure_sentry

# Configure with custom settings
configure_sentry(
    dsn="your_sentry_dsn",
    environment="production",
    release="1.0.0",
    sample_rate=1.0,  # Capture all errors
    traces_sample_rate=0.1  # Sample 10% of performance data
)
```

## What Gets Tracked

### 🔴 Errors and Exceptions

**Automatic Capture**:
- API connection failures
- Video generation errors
- File system issues
- Network timeouts
- Invalid user input

**Example Error Context**:
```json
{
  "exception": "MusicSourceError: API request failed: 500",
  "context": {
    "operation": "track_search",
    "style": "chill",
    "duration": 120,
    "api_endpoint": "/search/text/"
  },
  "breadcrumbs": [
    "Starting CLI setup",
    "Music manager configured", 
    "Searching for track",
    "Freesound API request: search/text/"
  ]
}
```

### 📊 Performance Monitoring

**Tracked Operations**:
- Video generation timing
- API response times
- Audio download speeds
- Cache hit rates

**Performance Transactions**:
```python
# Automatic transaction tracking
with SentryTransaction("generate_video", "video_generation"):
    video_path = await generator.generate_video(...)
```

### 🍞 Breadcrumbs

**Operation Tracking**:
```python
add_breadcrumb("Starting video generation", category="video_generation")
add_breadcrumb("Track found", category="music_discovery", data={"track_id": "123"})
add_breadcrumb("Audio downloaded", category="audio_download", data={"size": 2048576})
```

### 🏷️ Tags and Context

**Automatic Tags**:
- `component`: lofi-video-generator
- `platform`: linux/macos/windows
- `style`: chill/upbeat/calming/etc
- `operation`: video_generation/music_discovery/etc

**System Context**:
- Operating system and version
- Python version and runtime
- Available memory and disk space
- FFmpeg version

## Privacy and Security

### Data Protection

**What's Included**:
- Error messages and stack traces
- System information (OS, Python version)
- Operation context (style, duration)
- Performance metrics

**What's Excluded**:
- API keys (automatically redacted)
- Personal file paths
- User credentials
- Audio content data

### Filtering

```python
def before_send(event, hint):
    # Remove sensitive information
    if "request" in event:
        request = event["request"]
        if "token=" in request.get("url", ""):
            request["url"] = request["url"].split("token=")[0] + "token=***"
    
    return event
```

## Viewing Error Data

### GitLab Error Tracking

**Access Dashboard**:
1. Visit: https://gitlab.com/your-project/-/error_tracking
2. View error frequency and trends
3. Get detailed stack traces
4. Track error resolution

**Error Details Include**:
- Full stack trace
- Environment information
- User actions leading to error
- Frequency and first/last occurrence
- Related errors and patterns

### Local Debugging

**Enable Debug Mode**:
```bash
export SENTRY_DEBUG=true
python3 lofi_cli.py generate 120 chill
```

**Check Error Logs**:
```bash
# View recent errors
tail -f ~/.local/share/lofi-channel/logs/error.log

# Search for specific errors
grep "MusicSourceError" ~/.local/share/lofi-channel/logs/error.log
```

## Error Categories

### Critical Errors (🔴)

**Video Generation Failures**:
- FFmpeg not found or failed
- Insufficient disk space
- Corrupted audio data
- File permission issues

**API Issues**:
- Invalid API keys
- Network connectivity problems
- Rate limit exceeded
- Service unavailable

### Warnings (🟡)

**Performance Issues**:
- Slow API responses
- Large cache sizes
- Memory usage warnings
- Disk space low

**Operational Warnings**:
- Fallback to cached data
- Retry attempts
- Quality degradation

### Info Events (🔵)

**Success Metrics**:
- Video generation completed
- Track discovery successful
- Cache hit rates
- Performance benchmarks

## Custom Error Tracking

### Manual Error Capture

```python
from src.sentry_config import capture_exception, capture_message

try:
    risky_operation()
except Exception as e:
    capture_exception(e, context={
        "operation": "custom_operation",
        "user_input": user_data
    })
```

### Custom Messages

```python
capture_message(
    "Custom operation completed",
    level="info",
    tags={"component": "custom"},
    context={"metrics": performance_data}
)
```

### Performance Tracking

```python
from src.sentry_config import SentryTransaction

with SentryTransaction("custom_operation", "background_task"):
    # Your operation here
    result = perform_operation()
```

## Alerting and Notifications

### Automatic Alerts

**Error Thresholds**:
- New error types
- Error rate spikes
- Performance degradation
- Critical system failures

**Notification Channels**:
- GitLab issues
- Email notifications
- Slack/Discord webhooks
- Custom integrations

### Custom Alerts

```python
# Set up custom error monitoring
if error_rate > threshold:
    capture_message(
        f"Error rate spike detected: {error_rate}",
        level="warning",
        tags={"alert": "error_rate_spike"}
    )
```

## Performance Optimization

### Monitoring Metrics

**Video Generation**:
- Average generation time
- Success/failure rates
- Resource usage patterns
- Bottleneck identification

**API Performance**:
- Response time trends
- Rate limit utilization
- Cache effectiveness
- Network latency

### Optimization Insights

**Based on Error Data**:
- Most common failure points
- Performance bottlenecks
- Resource usage patterns
- User behavior analysis

## Troubleshooting Error Tracking

### Common Issues

**Sentry Not Initializing**:
```bash
# Check configuration
echo "SENTRY_DSN: $SENTRY_DSN"
echo "ENABLE_ERROR_TRACKING: $ENABLE_ERROR_TRACKING"

# Test connection
python3 -c "
from src.sentry_config import configure_sentry
configure_sentry(debug=True)
"
```

**No Errors Appearing**:
- Verify DSN is correct
- Check environment settings
- Ensure errors are actually occurring
- Verify network connectivity

**Too Many Errors**:
```bash
# Reduce sample rate
export SENTRY_SAMPLE_RATE=0.1  # 10% sampling
```

### Debug Mode

```python
# Enable verbose Sentry logging
configure_sentry(
    debug=True,
    sample_rate=1.0,
    traces_sample_rate=1.0
)
```

## Best Practices

### Error Handling

1. **Graceful Degradation**: Handle errors without crashing
2. **Contextual Information**: Include relevant operation details
3. **User-Friendly Messages**: Don't expose technical details to users
4. **Proper Logging**: Use appropriate log levels

### Performance Monitoring

1. **Sample Appropriately**: Don't track every operation in production
2. **Focus on Critical Paths**: Monitor key user journeys
3. **Set Baselines**: Establish performance expectations
4. **Regular Review**: Analyze trends and patterns

### Privacy Compliance

1. **Data Minimization**: Only collect necessary information
2. **Sensitive Data**: Filter out personal information
3. **Retention Policies**: Set appropriate data retention periods
4. **User Consent**: Inform users about error tracking

## Integration Examples

### CI/CD Pipeline

```yaml
# .gitlab-ci.yml
test:
  script:
    - export SENTRY_ENVIRONMENT=ci
    - export SENTRY_RELEASE=$CI_COMMIT_SHA
    - python3 -m pytest
  after_script:
    - python3 -c "
      from src.sentry_config import capture_message
      capture_message('CI pipeline completed', level='info')
      "
```

### Production Deployment

```bash
# Set production environment
export SENTRY_ENVIRONMENT=production
export SENTRY_RELEASE=$(git rev-parse HEAD)

# Deploy with error tracking
python3 lofi_cli.py generate 120 chill
```

This comprehensive error tracking system provides real-time insights into application health, helping maintain high reliability and performance for the Lo-Fi Video Generator.
