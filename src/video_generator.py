"""
Video generator for lo-fi music videos.
"""

import asyncio
import subprocess
import tempfile
from pathlib import Path
from typing import Op<PERSON>, Tuple, List
import json
import random
import logging

logger = logging.getLogger(__name__)

from .music_sources.base import Track, LoFiStyle


class LoFiVideoGenerator:
    """Generate lo-fi music videos with visual backgrounds."""
    
    # Style-specific visual themes
    STYLE_VISUALS = {
        LoFiStyle.UPBEAT: {
            'colors': ['#FFB6C1', '#87CEEB', '#98FB98', '#F0E68C'],
            'animation': 'bouncy',
            'elements': ['geometric', 'particles', 'waves']
        },
        LoFiStyle.CALMING: {
            'colors': ['#E6E6FA', '#F0F8FF', '#F5F5DC', '#FFF8DC'],
            'animation': 'slow',
            'elements': ['clouds', 'water', 'gradients']
        },
        LoFiStyle.CHILL: {
            'colors': ['#4682B4', '#708090', '#778899', '#B0C4DE'],
            'animation': 'smooth',
            'elements': ['abstract', 'flowing', 'minimal']
        },
        LoFiStyle.DREAMY: {
            'colors': ['#DDA0DD', '#E6E6FA', '#F0E68C', '#FFB6C1'],
            'animation': 'floating',
            'elements': ['stars', 'nebula', 'soft_shapes']
        },
        LoFiStyle.NOSTALGIC: {
            'colors': ['#D2B48C', '#F5DEB3', '#DEB887', '#CD853F'],
            'animation': 'vintage',
            'elements': ['film_grain', 'sepia', 'old_photos']
        },
        LoFiStyle.FOCUS: {
            'colors': ['#2F4F4F', '#696969', '#708090', '#A9A9A9'],
            'animation': 'steady',
            'elements': ['grid', 'lines', 'minimal']
        },
        LoFiStyle.STUDY: {
            'colors': ['#F5F5F5', '#DCDCDC', '#D3D3D3', '#C0C0C0'],
            'animation': 'subtle',
            'elements': ['books', 'desk', 'minimal']
        },
        LoFiStyle.RELAXING: {
            'colors': ['#98FB98', '#90EE90', '#8FBC8F', '#20B2AA'],
            'animation': 'gentle',
            'elements': ['nature', 'leaves', 'zen']
        },
        LoFiStyle.AMBIENT: {
            'colors': ['#191970', '#483D8B', '#6A5ACD', '#9370DB'],
            'animation': 'atmospheric',
            'elements': ['space', 'atmosphere', 'depth']
        },
        LoFiStyle.JAZZY: {
            'colors': ['#8B4513', '#A0522D', '#CD853F', '#D2691E'],
            'animation': 'rhythmic',
            'elements': ['instruments', 'notes', 'vintage']
        }
    }
    
    def __init__(self, output_dir: str = "videos"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.temp_dir = Path(tempfile.gettempdir()) / "lofi_videos"
        self.temp_dir.mkdir(exist_ok=True)
    
    def check_dependencies(self) -> Tuple[bool, List[str]]:
        """Check if required dependencies are installed."""
        missing = []
        
        # Check for FFmpeg
        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            missing.append('ffmpeg')
        
        return len(missing) == 0, missing
    
    async def generate_video(
        self,
        track: Track,
        audio_data: bytes,
        duration: float,
        style: LoFiStyle,
        output_filename: Optional[str] = None
    ) -> Optional[Path]:
        """Generate a lo-fi video with the given track and style."""

        if not output_filename:
            safe_title = "".join(c for c in track.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            output_filename = f"{safe_title}_{style.value}_{int(duration)}s.mp4"

        output_path = self.output_dir / output_filename

        # Create temporary audio file
        audio_file = self.temp_dir / f"audio_{track.id}.mp3"
        try:
            with open(audio_file, 'wb') as f:
                f.write(audio_data)
        except Exception as e:
            logger.error(f"Error creating audio file: {e}")
            return None

        try:
            # Generate visual background
            visual_path = await self._generate_visual_background(style, duration)
            
            if not visual_path:
                # Fallback to simple color background
                visual_path = await self._generate_simple_background(style, duration)
            
            # Combine audio and visual
            success = await self._combine_audio_visual(
                audio_file, visual_path, output_path, duration, track, style
            )
            
            if success:
                return output_path
            else:
                return None
                
        finally:
            # Cleanup temporary files
            if audio_file.exists():
                audio_file.unlink()
    
    async def generate_video_from_multiple_tracks(
        self,
        tracks: List[Track],
        audio_data_list: List[bytes],
        duration: float,
        style: LoFiStyle,
        output_filename: Optional[str] = None
    ) -> Optional[Path]:
        """Generate a lo-fi video from multiple concatenated tracks."""
        
        if not tracks or not audio_data_list:
            return None
            
        if len(tracks) != len(audio_data_list):
            logger.error("❌ Mismatch between number of tracks and audio data")
            return None


        if not output_filename:
            # Use first track title for filename
            safe_title = "".join(c for c in tracks[0].title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            output_filename = f"{safe_title}_mix_{style.value}_{int(duration)}s.mp4"

        output_path = self.output_dir / output_filename

        # Create temporary audio files for each track
        temp_audio_files = []
        concatenated_audio = None
        try:
            for i, (track, audio_data) in enumerate(zip(tracks, audio_data_list)):
                audio_file = self.temp_dir / f"audio_{track.id}_{i}.mp3"
                with open(audio_file, 'wb') as f:
                    f.write(audio_data)
                temp_audio_files.append(audio_file)
                logger.info(f"Audio file {i+1} created")

            # Concatenate audio files
            logger.info("🔗 Concatenating audio tracks...")
            concatenated_audio = await self._concatenate_audio_files(temp_audio_files, duration)
            
            if not concatenated_audio:
                logger.error("❌ Failed to concatenate audio files")
                return None

            # Generate visual background
            visual_path = await self._generate_visual_background(style, duration)
            
            if not visual_path:
                # Fallback to simple color background
                visual_path = await self._generate_simple_background(style, duration)
            
            # Combine concatenated audio and visual
            success = await self._combine_audio_visual_multi_track(
                concatenated_audio, visual_path, output_path, duration, tracks, style
            )
            
            if success:
                return output_path
            else:
                return None
                
        finally:
            # Cleanup temporary files
            for audio_file in temp_audio_files:
                if audio_file.exists():
                    audio_file.unlink()
            # Also cleanup concatenated audio file
            if concatenated_audio and concatenated_audio.exists():
                concatenated_audio.unlink()
    
    async def _concatenate_audio_files(self, audio_files: List[Path], target_duration: float) -> Optional[Path]:
        """Concatenate multiple audio files and trim to target duration."""
        if not audio_files:
            return None
            
        output_path = self.temp_dir / f"concatenated_{random.randint(1000, 9999)}.mp3"
        
        # Create FFmpeg filter for concatenation
        # Build input arguments
        cmd = ['ffmpeg', '-y']
        
        # Add all input files
        for audio_file in audio_files:
            cmd.extend(['-i', str(audio_file)])
        
        # Create filter complex for concatenation
        filter_inputs = ''.join(f'[{i}:0]' for i in range(len(audio_files)))
        filter_complex = f'{filter_inputs}concat=n={len(audio_files)}:v=0:a=1[out]'
        
        cmd.extend([
            '-filter_complex', filter_complex,
            '-map', '[out]',
            '-t', str(target_duration),  # Trim to target duration
            '-c:a', 'mp3',
            str(output_path)
        ])
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and output_path.exists():
                logger.info(f"✅ Concatenated {len(audio_files)} tracks")
                return output_path
            else:
                logger.error(f"FFmpeg concatenation error: {stderr.decode()}")
                return None
                
        except Exception as e:
            logger.error(f"Error concatenating audio: {e}")
            return None
    
    async def _combine_audio_visual_multi_track(
        self, 
        audio_path: Path, 
        visual_path: Path, 
        output_path: Path, 
        duration: float,
        tracks: List[Track],
        style: LoFiStyle
    ) -> bool:
        """Combine audio and visual into final video for multi-track videos."""
        
        # Create text overlay showing it's a mix of multiple tracks
        if len(tracks) == 1:
            title_text = tracks[0].title.replace("'", "\\'").replace('"', '\\"')
            artist_text = tracks[0].artist.replace("'", "\\'").replace('"', '\\"')
        else:
            title_text = f"Lo-Fi Mix ({len(tracks)} tracks)"
            artist_text = f"Various Artists - {style.value.title()} Style"
        
        # Create text overlay filter
        text_filter = (
            f"drawtext=text='{title_text}':fontcolor=white:fontsize=48:"
            f"x=(w-text_w)/2:y=h/2-100:enable='between(t,2,8)',"
            f"drawtext=text='{artist_text}':fontcolor=white:fontsize=32:"
            f"x=(w-text_w)/2:y=h/2-40:enable='between(t,2,8)'"
        )
        
        cmd = [
            'ffmpeg', '-y',
            '-i', str(visual_path),
            '-i', str(audio_path),
            '-filter_complex', text_filter,
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-shortest',
            '-t', str(duration),
            str(output_path)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and output_path.exists():
                return True
            else:
                logger.error(f"FFmpeg combine error: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"Error combining audio/visual: {e}")
            return False
    
    async def _generate_visual_background(self, style: LoFiStyle, duration: float) -> Optional[Path]:
        """Generate visual background based on style."""
        visual_config = self.STYLE_VISUALS.get(style, self.STYLE_VISUALS[LoFiStyle.CHILL])
        
        # For now, create a simple animated background using FFmpeg
        # In a full implementation, you might use more sophisticated tools
        return await self._create_animated_background(visual_config, duration)
    
    async def _create_animated_background(self, visual_config: dict, duration: float) -> Optional[Path]:
        """Create animated background using FFmpeg filters."""
        output_path = self.temp_dir / f"background_{random.randint(1000, 9999)}.mp4"
        
        # Get primary color for the style
        primary_color = visual_config['colors'][0].replace('#', '')
        secondary_color = visual_config['colors'][1].replace('#', '') if len(visual_config['colors']) > 1 else primary_color
        
        # Create animated gradient background
        cmd = [
            'ffmpeg', '-y',
            '-f', 'lavfi',
            '-i', f'color=c=0x{primary_color}:size=1920x1080:duration={duration}',
            '-f', 'lavfi', 
            '-i', f'color=c=0x{secondary_color}:size=1920x1080:duration={duration}',
            '-filter_complex', 
            f'[0][1]blend=all_mode=overlay:all_opacity=0.5,format=yuv420p',
            '-t', str(duration),
            '-r', '30',
            str(output_path)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and output_path.exists():
                return output_path
            else:
                logger.error(f"FFmpeg error: {stderr.decode()}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating background: {e}")
            return None
    
    async def _generate_simple_background(self, style: LoFiStyle, duration: float) -> Optional[Path]:
        """Generate simple solid color background as fallback."""
        visual_config = self.STYLE_VISUALS.get(style, self.STYLE_VISUALS[LoFiStyle.CHILL])
        color = visual_config['colors'][0].replace('#', '')
        
        output_path = self.temp_dir / f"simple_bg_{random.randint(1000, 9999)}.mp4"
        
        cmd = [
            'ffmpeg', '-y',
            '-f', 'lavfi',
            '-i', f'color=c=0x{color}:size=1920x1080:duration={duration}',
            '-t', str(duration),
            '-r', '30',
            str(output_path)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await process.communicate()
            
            if process.returncode == 0 and output_path.exists():
                return output_path
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error creating simple background: {e}")
            return None
    
    async def _combine_audio_visual(
        self, 
        audio_path: Path, 
        visual_path: Path, 
        output_path: Path, 
        duration: float,
        track: Track,
        style: LoFiStyle
    ) -> bool:
        """Combine audio and visual into final video."""
        
        # Add text overlay with track info
        title_text = track.title.replace("'", "\\'").replace('"', '\\"')
        artist_text = track.artist.replace("'", "\\'").replace('"', '\\"')
        
        # Create text overlay filter
        text_filter = (
            f"drawtext=text='{title_text}':fontcolor=white:fontsize=48:"
            f"x=(w-text_w)/2:y=h/2-100:enable='between(t,2,8)',"
            f"drawtext=text='by {artist_text}':fontcolor=white:fontsize=32:"
            f"x=(w-text_w)/2:y=h/2-40:enable='between(t,2,8)'"
        )
        
        cmd = [
            'ffmpeg', '-y',
            '-i', str(visual_path),
            '-i', str(audio_path),
            '-filter_complex', text_filter,
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-shortest',
            '-t', str(duration),
            str(output_path)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and output_path.exists():
                return True
            else:
                logger.error(f"FFmpeg combine error: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"Error combining audio/visual: {e}")
            return False
    
    def get_video_info(self, video_path: Path) -> Optional[dict]:
        """Get information about generated video."""
        if not video_path.exists():
            return None
        
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', str(video_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return json.loads(result.stdout)
            
        except (subprocess.CalledProcessError, json.JSONDecodeError):
            return None
    
    def cleanup_temp_files(self):
        """Clean up temporary files."""
        for file in self.temp_dir.glob("*"):
            try:
                file.unlink()
            except Exception:
                pass
