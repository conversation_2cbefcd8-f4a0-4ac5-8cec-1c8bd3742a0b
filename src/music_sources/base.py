"""
Base classes for music source implementations.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional, Dict, Any
import asyncio

import logging

logger = logging.getLogger(__name__)


class LicenseType(Enum):
    """Supported license types for tracks."""
    CC0 = "cc0"  # Public domain
    CC_BY = "cc_by"  # Attribution required
    CC_BY_NC = "cc_by_nc"  # Attribution required, non-commercial
    CUSTOM = "custom"  # Custom license terms


class LoFiStyle(Enum):
    """Lo-fi music styles/moods."""
    UPBEAT = "upbeat"
    CALMING = "calming"
    CHILL = "chill"
    DREAMY = "dreamy"
    NOSTALGIC = "nostalgic"
    FOCUS = "focus"
    STUDY = "study"
    RELAXING = "relaxing"
    AMBIENT = "ambient"
    JAZZY = "jazzy"


# Style-specific search keywords
LOFI_STYLE_KEYWORDS = {
    LoFiStyle.UPBEAT: ['upbeat', 'energetic', 'positive', 'happy', 'bouncy', 'groove'],
    LoFiStyle.CALMING: ['calm', 'peaceful', 'serene', 'tranquil', 'soothing', 'gentle'],
    LoFiStyle.CHILL: ['chill', 'laid-back', 'relaxed', 'mellow', 'smooth', 'cool'],
    LoFiStyle.DREAMY: ['dreamy', 'ethereal', 'floating', 'atmospheric', 'spacey', 'soft'],
    LoFiStyle.NOSTALGIC: ['nostalgic', 'vintage', 'retro', 'memories', 'warm', 'classic'],
    LoFiStyle.FOCUS: ['focus', 'concentration', 'work', 'productivity', 'minimal', 'steady'],
    LoFiStyle.STUDY: ['study', 'reading', 'learning', 'quiet', 'background', 'subtle'],
    LoFiStyle.RELAXING: ['relaxing', 'stress-relief', 'meditation', 'zen', 'mindful', 'slow'],
    LoFiStyle.AMBIENT: ['ambient', 'atmospheric', 'soundscape', 'texture', 'space', 'drone'],
    LoFiStyle.JAZZY: ['jazz', 'swing', 'blues', 'saxophone', 'piano', 'improvisation']
}


@dataclass
class Track:
    """Represents a music track from any source."""
    id: str
    title: str
    artist: str
    duration: float  # in seconds
    preview_url: Optional[str] = None
    download_url: Optional[str] = None
    license_type: LicenseType = LicenseType.CUSTOM
    attribution_text: Optional[str] = None
    tags: List[str] = None
    source: str = ""  # Source platform name
    bpm: Optional[int] = None
    genre: Optional[str] = None
    mood: Optional[str] = None
    file_size: Optional[int] = None  # in bytes
    file_format: Optional[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []


@dataclass
class SearchQuery:
    """Search parameters for finding lo-fi tracks."""
    query: str = ""
    style: Optional[LoFiStyle] = None
    min_duration: Optional[float] = None
    max_duration: Optional[float] = None
    license_types: List[LicenseType] = None
    tags: List[str] = None
    limit: int = 20
    offset: int = 0
    lofi_only: bool = True  # Only search for lo-fi music

    def __post_init__(self):
        if self.license_types is None:
            self.license_types = []
        if self.tags is None:
            self.tags = []

        # Always include lo-fi tags when lofi_only is True
        if self.lofi_only:
            lofi_tags = ['lofi', 'lo-fi', 'chill', 'ambient', 'beats', 'hip-hop']
            self.tags.extend([tag for tag in lofi_tags if tag not in self.tags])


@dataclass
class SearchResult:
    """Result from a track search."""
    tracks: List[Track]
    total_count: int
    has_more: bool
    next_offset: Optional[int] = None


class MusicSourceError(Exception):
    """Base exception for music source operations."""
    pass


class RateLimitError(MusicSourceError):
    """Raised when API rate limit is exceeded."""
    def __init__(self, message: str, retry_after: Optional[int] = None):
        super().__init__(message)
        self.retry_after = retry_after


class AuthenticationError(MusicSourceError):
    """Raised when authentication fails."""
    pass


class MusicSource(ABC):
    """Abstract base class for music source implementations."""
    
    def __init__(self, api_key: str, **kwargs):
        self.api_key = api_key
        self.session = None
        self._rate_limiter = None
    
    @property
    @abstractmethod
    def source_name(self) -> str:
        """Return the name of this music source."""
        pass
    
    @abstractmethod
    async def search(self, query: SearchQuery) -> SearchResult:
        """Search for tracks matching the given query."""
        pass
    
    @abstractmethod
    async def get_track(self, track_id: str) -> Optional[Track]:
        """Get detailed information about a specific track."""
        pass
    
    async def download_preview(self, track: Track) -> Optional[bytes]:
        """Download preview audio data for a track."""
        if not track.preview_url:
            return None
        
        # Implementation would depend on the specific source
        # This is a placeholder
        return None
    
    async def download_full(self, track: Track) -> Optional[bytes]:
        """Download full quality audio data for a track."""
        if not track.download_url:
            return None
        
        # Implementation would depend on the specific source
        # This is a placeholder
        return None
    
    async def close(self):
        """Clean up resources."""
        if self.session:
            await self.session.close()


class RateLimiter:
    """Simple rate limiter for API requests."""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.requests = []
    
    async def acquire(self):
        """Wait if necessary to respect rate limits."""
        import time
        
        now = time.time()
        # Remove requests older than 1 minute
        self.requests = [req_time for req_time in self.requests if now - req_time < 60]
        
        if len(self.requests) >= self.requests_per_minute:
            # Wait until the oldest request is more than 1 minute old
            sleep_time = 60 - (now - self.requests[0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
        
        self.requests.append(now)


def get_attribution_text(track: Track) -> Optional[str]:
    """Generate proper attribution text for a track based on its license."""
    if track.license_type == LicenseType.CC0:
        return None  # No attribution required
    
    if track.attribution_text:
        return track.attribution_text
    
    # Generate standard attribution
    if track.license_type in [LicenseType.CC_BY, LicenseType.CC_BY_NC]:
        return f'"{track.title}" by {track.artist} from {track.source} licensed under {track.license_type.value.upper()}'
    
    return f'"{track.title}" by {track.artist} from {track.source}'


def is_commercial_use_allowed(track: Track) -> bool:
    """Check if a track can be used for commercial purposes."""
    return track.license_type in [LicenseType.CC0, LicenseType.CC_BY, LicenseType.CUSTOM]
