"""
Specialized lo-fi music manager for video generation.
"""

import asyncio
from typing import List, Optional, Dict, Any
from pathlib import Path
import random
import logging

logger = logging.getLogger(__name__)

from .music_sources.manager import MusicSourceManager
from .music_sources.base import SearchQuery, Track, LicenseType, LoFiStyle, LOFI_STYLE_KEYWORDS


class LoFiMusicManager:
    """Specialized manager for lo-fi music discovery and curation."""

    # Use style keywords from base module
    STYLE_KEYWORDS = LOFI_STYLE_KEYWORDS
    
    # BPM ranges for different styles
    STYLE_BPM_RANGES = {
        LoFiStyle.UPBEAT: (90, 120),
        LoFiStyle.CALMING: (60, 80),
        LoFiStyle.CHILL: (70, 90),
        LoFiStyle.DREAMY: (50, 70),
        LoFiStyle.NOSTALGIC: (70, 95),
        LoFiStyle.FOCUS: (80, 100),
        LoFiStyle.STUDY: (65, 85),
        LoFiStyle.RELAXING: (50, 75),
        LoFiStyle.AMBIENT: (40, 70),
        LoFiStyle.JAZZY: (80, 110)
    }
    
    def __init__(self, cache_dir: str = "lofi_cache"):
        self.manager = MusicSourceManager(cache_dir)
        self._curated_tracks: Dict[LoFiStyle, List[Track]] = {}
    
    def configure_sources(self, freesound_api_key: str, **kwargs):
        """Configure music sources."""
        self.manager.configure_freesound(freesound_api_key)
        # Add other sources as needed
    
    async def find_lofi_for_style(self, style: LoFiStyle, duration: float, limit: int = 20) -> List[Track]:
        """Find lo-fi tracks matching a specific style and duration."""
        
        # Build style-specific search query
        style_keywords = self.STYLE_KEYWORDS.get(style, [])
        
        # Create search query with style-specific terms
        query = SearchQuery(
            query=f"lofi {style.value}",  # Simplified query
            style=style,
            min_duration=max(30.0, duration * 0.5),  # More flexible duration
            max_duration=duration * 3.0,  # Even more flexible
            license_types=[],  # Don't filter by license in API call
            tags=['lofi', 'lo-fi', style.value],  # Fewer required tags
            limit=limit,
            lofi_only=True
        )
        
        # Search all sources
        results = await self.manager.search_all_sources(query)
        
        # Combine and filter results
        all_tracks = []
        for source_name, result in results.items():
            all_tracks.extend(result.tracks)
        
        # Filter for lo-fi characteristics
        lofi_tracks = self._filter_lofi_tracks(all_tracks, style)
        
        # Sort by relevance to style and duration
        lofi_tracks.sort(key=lambda t: self._calculate_style_score(t, style, duration), reverse=True)
        
        return lofi_tracks[:limit]
    
    def _filter_lofi_tracks(self, tracks: List[Track], style: LoFiStyle) -> List[Track]:
        """Filter tracks to ensure they are actually lo-fi."""
        lofi_tracks = []
        
        for track in tracks:
            if self._is_lofi_track(track, style):
                lofi_tracks.append(track)
        
        return lofi_tracks
    
    def _is_lofi_track(self, track: Track, style: LoFiStyle) -> bool:
        """Check if a track is actually lo-fi music."""
        # Check tags for lo-fi indicators
        lofi_indicators = [
            'lofi', 'lo-fi', 'chill', 'ambient', 'beats', 'hip-hop',
            'downtempo', 'chillhop', 'jazzhop', 'study', 'relax'
        ]
        
        track_tags_lower = [tag.lower() for tag in track.tags]
        title_lower = track.title.lower()
        
        # Must have at least one lo-fi indicator
        has_lofi_indicator = any(
            indicator in track_tags_lower or indicator in title_lower
            for indicator in lofi_indicators
        )
        
        if not has_lofi_indicator:
            return False
        
        # Exclude non-lo-fi genres
        excluded_genres = [
            'rock', 'metal', 'punk', 'techno', 'house', 'trance',
            'dubstep', 'drum and bass', 'hardcore', 'classical'
        ]
        
        has_excluded_genre = any(
            genre in track_tags_lower or genre in title_lower
            for genre in excluded_genres
        )
        
        if has_excluded_genre:
            return False
        
        # Duration check (lo-fi tracks are usually 1-10 minutes)
        if track.duration < 30 or track.duration > 600:
            return False
        
        return True
    
    def _calculate_style_score(self, track: Track, style: LoFiStyle, target_duration: float) -> float:
        """Calculate how well a track matches the requested style."""
        score = 0.0
        
        # Style keyword matching
        style_keywords = self.STYLE_KEYWORDS.get(style, [])
        track_text = f"{track.title} {' '.join(track.tags)}".lower()
        
        for keyword in style_keywords:
            if keyword in track_text:
                score += 1.0
        
        # Duration preference (prefer tracks close to target duration)
        duration_diff = abs(track.duration - target_duration)
        duration_score = max(0, 1.0 - (duration_diff / target_duration))
        score += duration_score * 2.0
        
        # License preference (CC0 is better than CC-BY)
        if track.license_type == LicenseType.CC0:
            score += 0.5
        
        # Source preference (some sources might be more reliable)
        if track.source == "Freesound":
            score += 0.3
        
        return score
    
    async def get_track_for_video(self, duration: float, style: LoFiStyle) -> Optional[Track]:
        """Get the best lo-fi track for a video of specified duration and style."""
        
        # Try to find tracks that match the duration closely
        tracks = await self.find_lofi_for_style(style, duration, limit=10)
        
        if not tracks:
            # Fallback to any chill lo-fi if no style-specific tracks found
            fallback_query = SearchQuery(
                query="lofi OR lo-fi OR chill",
                min_duration=30.0,
                max_duration=600.0,  # Up to 10 minutes
                license_types=[],  # Don't filter by license
                limit=10,
                lofi_only=False  # Don't enforce lo-fi only tags
            )
            
            results = await self.manager.search_all_sources(fallback_query)
            tracks = []
            for result in results.values():
                tracks.extend(result.tracks)
        
        if not tracks:
            return None
        
        # Select the best matching track
        best_track = max(tracks, key=lambda t: self._calculate_style_score(t, style, duration))
        
        return best_track
    
    async def get_tracks_for_long_video(self, duration: float, style: LoFiStyle) -> List[Track]:
        """Get multiple lo-fi tracks for a long video that requires track concatenation."""
        
        # For long videos, we need multiple tracks to fill the duration
        target_tracks = []
        remaining_duration = duration
        max_attempts = 20  # Prevent infinite loops
        attempts = 0
        
        logger.info(f"🎵 Finding multiple tracks to fill {duration:.0f}s ({duration/60:.1f} minutes)...")
        
        while remaining_duration > 30 and attempts < max_attempts:
            attempts += 1
            
            # Find tracks for the remaining duration, but prefer tracks 2-5 minutes long
            preferred_duration = min(300, max(120, remaining_duration / 3))  # 2-5 minutes preferred
            
            # Get available tracks, excluding ones we already selected
            available_tracks = await self.find_lofi_for_style(style, preferred_duration, limit=20)
            
            # Filter out tracks we've already selected
            used_track_ids = {track.id for track in target_tracks}
            available_tracks = [t for t in available_tracks if t.id not in used_track_ids]
            
            if not available_tracks:
                # Fallback to any lo-fi if no style-specific tracks available
                fallback_query = SearchQuery(
                    query="lofi OR lo-fi OR chill",
                    min_duration=60.0,
                    max_duration=300.0,
                    license_types=[],
                    limit=15,
                    lofi_only=False
                )
                
                results = await self.manager.search_all_sources(fallback_query)
                available_tracks = []
                for result in results.values():
                    available_tracks.extend(result.tracks)
                
                # Filter out already used tracks
                available_tracks = [t for t in available_tracks if t.id not in used_track_ids]
            
            if not available_tracks:
                logger.warning(f"⚠️  Could not find more tracks after {len(target_tracks)} tracks")
                break
            
            # Select the best track for the remaining duration
            best_track = max(available_tracks, 
                           key=lambda t: self._calculate_style_score(t, style, remaining_duration))
            
            target_tracks.append(best_track)
            remaining_duration -= best_track.duration
            
            logger.info(f"   ✅ Added: '{best_track.title}' ({best_track.duration:.1f}s) - {remaining_duration:.1f}s remaining")
        
        # If we still have remaining time and have at least one track, repeat the last track
        if remaining_duration > 30 and target_tracks:
            # Find the longest track to repeat
            longest_track = max(target_tracks, key=lambda t: t.duration)
            repeats_needed = int(remaining_duration / longest_track.duration) + 1
            
            for _ in range(repeats_needed):
                if remaining_duration > 30:
                    target_tracks.append(longest_track)
                    remaining_duration -= longest_track.duration
                    logger.info(f"   🔄 Repeating: '{longest_track.title}' - {remaining_duration:.1f}s remaining")
        
        total_duration = sum(track.duration for track in target_tracks)
        logger.info(f"🎼 Selected {len(target_tracks)} tracks for total duration: {total_duration:.1f}s")
        
        return target_tracks
    
    async def download_track_audio(self, track: Track) -> Optional[bytes]:
        """Download audio data for a track."""
        return await self.manager.get_audio_preview(track)
    
    async def download_multiple_tracks_audio(self, tracks: List[Track]) -> List[bytes]:
        """Download audio data for multiple tracks."""
        audio_data_list = []
        
        for i, track in enumerate(tracks, 1):
            logger.info(f"⬇️  Downloading track {i}/{len(tracks)}: {track.title}")
            audio_data = await self.download_track_audio(track)
            if audio_data:
                audio_data_list.append(audio_data)
                logger.info(f"   ✅ Downloaded {len(audio_data)} bytes")
            else:
                logger.error(f"   ❌ Failed to download {track.title}")
                # Continue with other tracks even if one fails
        
        return audio_data_list
    
    def get_attribution_text(self, track: Track) -> Optional[str]:
        """Get attribution text for a track."""
        return self.manager.get_attribution_requirements([track])
    
    def get_attribution_text_for_multiple(self, tracks: List[Track]) -> List[str]:
        """Get attribution text for multiple tracks."""
        return self.manager.get_attribution_requirements(tracks)
    
    async def curate_style_playlist(self, style: LoFiStyle, count: int = 50) -> List[Track]:
        """Curate a playlist of tracks for a specific style."""
        if style in self._curated_tracks and len(self._curated_tracks[style]) >= count:
            return random.sample(self._curated_tracks[style], min(count, len(self._curated_tracks[style])))
        
        # Find tracks for this style with various durations
        all_tracks = []
        for duration in [120, 180, 240, 300]:  # 2, 3, 4, 5 minutes
            tracks = await self.find_lofi_for_style(style, duration, limit=15)
            all_tracks.extend(tracks)
        
        # Deduplicate
        seen = set()
        unique_tracks = []
        for track in all_tracks:
            track_key = f"{track.title}:{track.artist}"
            if track_key not in seen:
                seen.add(track_key)
                unique_tracks.append(track)
        
        # Cache the curated tracks
        self._curated_tracks[style] = unique_tracks
        
        return unique_tracks[:count]
    
    async def close(self):
        """Close the manager."""
        await self.manager.close()
    
    @staticmethod
    def get_available_styles() -> List[str]:
        """Get list of available lo-fi styles."""
        return [style.value for style in LoFiStyle]
    
    @staticmethod
    def parse_style(style_str: str) -> Optional[LoFiStyle]:
        """Parse style string to LoFiStyle enum."""
        try:
            return LoFiStyle(style_str.lower())
        except ValueError:
            return None
