         198385 function calls (193962 primitive calls) in 25.316 seconds

   Ordered by: internal time

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
      279   24.911    0.089   46.985    0.168 {method 'poll' of 'select.epoll' objects}
        2    0.090    0.045    0.090    0.045 {built-in method posix.waitpid}
        2    0.038    0.019    0.038    0.019 {method 'poll' of 'select.poll' objects}
      195    0.024    0.000    0.024    0.000 {built-in method marshal.loads}
        2    0.014    0.007    0.014    0.007 {method 'set_default_verify_paths' of '_ssl._SSLContext' objects}
       34    0.010    0.000    0.010    0.000 {built-in method _imp.create_dynamic}
       99    0.010    0.000    0.010    0.000 {built-in method builtins.compile}
      195    0.009    0.000    0.033    0.000 <frozen importlib._bootstrap_external>:782(_compile_bytecode)
        1    0.008    0.008    0.011    0.011 {built-in method _socket.getaddrinfo}
  665/662    0.008    0.000    0.037    0.000 {built-in method builtins.__build_class__}
     1159    0.007    0.000    0.007    0.000 {built-in method posix.stat}
       34    0.005    0.000    0.006    0.000 {built-in method _imp.exec_dynamic}
  288/102    0.004    0.000    0.011    0.000 _parser.py:511(_parse)
        1    0.004    0.004    0.004    0.004 {method 'write' of '_io.BufferedWriter' objects}
      195    0.004    0.000    0.004    0.000 {built-in method _io.open_code}
        4    0.004    0.001    0.016    0.004 thread.py:54(run)
 1061/955    0.004    0.000    0.006    0.000 {built-in method __new__ of type object at 0x5b7ecad30ae0}
      195    0.004    0.000    0.004    0.000 {method 'read' of '_io.BufferedReader' objects}
      578    0.004    0.000    0.016    0.000 <frozen importlib._bootstrap_external>:1624(find_spec)
        3    0.004    0.001    0.004    0.001 {method 'do_handshake' of '_ssl._SSLSocket' objects}
      279    0.003    0.000   49.640    0.178 base_events.py:1962(_run_once)
    19351    0.003    0.000    0.003    0.000 {built-in method builtins.isinstance}
     2986    0.002    0.000    0.004    0.000 <frozen importlib._bootstrap_external>:131(_path_join)
      279    0.002    0.000   47.003    0.168 selectors.py:435(select)
    201/1    0.002    0.000   25.107   25.107 {built-in method builtins.exec}
       20    0.002    0.000    0.005    0.000 enum.py:1753(convert_class)
   483/85    0.002    0.000    0.005    0.000 _compiler.py:37(_compile)
12045/11529    0.002    0.000    0.002    0.000 {built-in method builtins.len}
        3    0.002    0.001    0.002    0.001 {built-in method _posixsubprocess.fork_exec}
    10655    0.002    0.000    0.002    0.000 {method 'append' of 'list' objects}
      819    0.002    0.000    0.005    0.000 typing.py:173(_type_check)
      347    0.002    0.000    0.007    0.000 typing.py:1423(__init__)
     2209    0.002    0.000    0.003    0.000 typing.py:1368(__setattr__)
       70    0.002    0.000    0.002    0.000 {built-in method builtins.eval}
      324    0.001    0.000    0.074    0.000 {method 'run' of '_contextvars.Context' objects}
      397    0.001    0.000    0.004    0.000 <frozen importlib._bootstrap>:304(acquire)
      195    0.001    0.000    0.047    0.000 <frozen importlib._bootstrap_external>:1093(get_code)
      249    0.001    0.000    0.021    0.000 <frozen importlib._bootstrap>:1240(_find_spec)
       36    0.001    0.000    0.002    0.000 _make.py:838(_create_slots_class)
    249/8    0.001    0.000    0.215    0.027 <frozen importlib._bootstrap>:1349(_find_and_load)
     5370    0.001    0.000    0.002    0.000 _parser.py:260(get)
   238/84    0.001    0.000    0.002    0.000 streams.py:688(read)
       82    0.001    0.000    0.001    0.000 {built-in method posix.read}
     2526    0.001    0.000    0.002    0.000 typing.py:1294(_is_dunder)
      193    0.001    0.000    0.002    0.000 _compiler.py:241(_optimize_charset)
1155/1065    0.001    0.000    0.016    0.000 typing.py:426(inner)
     3759    0.001    0.000    0.001    0.000 {method 'join' of 'str' objects}
     4254    0.001    0.000    0.001    0.000 {built-in method builtins.getattr}
     2942    0.001    0.000    0.001    0.000 _parser.py:167(__getitem__)
     6081    0.001    0.000    0.001    0.000 _parser.py:239(__next)
      390    0.001    0.000    0.003    0.000 <frozen importlib._bootstrap_external>:513(cache_from_source)
      397    0.001    0.000    0.001    0.000 <frozen importlib._bootstrap>:426(_get_module_lock)
       22    0.001    0.000    0.003    0.000 __init__.py:358(namedtuple)
      245    0.001    0.000    0.018    0.000 <frozen importlib._bootstrap_external>:1522(_get_spec)
     6372    0.001    0.000    0.001    0.000 {method 'rstrip' of 'str' objects}
      343    0.001    0.000    0.021    0.000 __init__.py:330(_compile)
       24    0.001    0.000    0.001    0.000 {built-in method posix.listdir}
  584/190    0.001    0.000    0.001    0.000 _parser.py:177(getwidth)
    249/8    0.001    0.000    0.215    0.027 <frozen importlib._bootstrap>:1304(_find_and_load_unlocked)
     5786    0.001    0.000    0.001    0.000 {method 'startswith' of 'str' objects}
      397    0.001    0.000    0.001    0.000 <frozen importlib._bootstrap>:372(release)
       76    0.001    0.000    0.004    0.000 unix_events.py:558(_read_ready)
      104    0.001    0.000    0.002    0.000 enum.py:251(__set_name__)
      198    0.001    0.000    0.001    0.000 {method '__exit__' of '_io._IOBase' objects}
     2206    0.001    0.000    0.001    0.000 {method 'get' of 'dict' objects}
    234/9    0.001    0.000    0.214    0.024 <frozen importlib._bootstrap>:911(_load_unlocked)
   558/17    0.001    0.000    0.210    0.012 <frozen importlib._bootstrap>:480(_call_with_frames_removed)
      234    0.001    0.000    0.004    0.000 <frozen importlib._bootstrap>:733(_init_module_attrs)
      2/1    0.001    0.000   25.059   25.059 lofi_cli.py:262(main)
      203    0.001    0.000    0.001    0.000 enum.py:354(__setitem__)
      397    0.001    0.000    0.002    0.000 <frozen importlib._bootstrap>:124(setdefault)
       13    0.001    0.000    0.004    0.000 enum.py:898(_convert_)
      195    0.001    0.000    0.009    0.000 <frozen importlib._bootstrap_external>:1214(get_data)
      235    0.001    0.000    0.001    0.000 base_events.py:852(_call_soon)
   220/85    0.001    0.000    0.011    0.000 _parser.py:451(_parse_sub)
       37    0.001    0.000    0.001    0.000 _make.py:261(_make_attr_tuple_class)
     2592    0.001    0.000    0.001    0.000 {built-in method builtins.hasattr}
       24    0.001    0.000    0.001    0.000 {method 'flush' of '_io.TextIOWrapper' objects}
      161    0.001    0.000    0.006    0.000 typing.py:753(Union)
      324    0.001    0.000    0.074    0.000 events.py:87(_run)
        4    0.001    0.000    0.001    0.000 {built-in method _thread.start_joinable_thread}
        5    0.001    0.000    0.001    0.000 threading.py:869(__init__)
      279    0.001    0.000    0.001    0.000 selector_events.py:740(_process_events)
      229    0.001    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:833(spec_from_file_location)
      361    0.001    0.000    0.002    0.000 typing.py:260(_collect_type_parameters)
      506    0.001    0.000    0.001    0.000 enum.py:829(__setattr__)
      156    0.001    0.000    0.001    0.000 streams.py:515(_wait_for_data)
       39    0.001    0.000    0.003    0.000 enum.py:498(__new__)
       37    0.001    0.000    0.002    0.000 _make.py:375(_transform_attrs)
     1854    0.001    0.000    0.001    0.000 {method 'rpartition' of 'str' objects}
       24    0.000    0.000    0.254    0.011 __init__.py:1(<module>)
    195/8    0.000    0.000    0.214    0.027 <frozen importlib._bootstrap_external>:1020(exec_module)
     1073    0.000    0.000    0.001    0.000 {built-in method builtins.setattr}
      390    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:137(_path_split)
        1    0.000    0.000    0.001    0.001 http_parser.py:238(HttpParser)
     3157    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:491(_verbose_message)
      346    0.000    0.000    0.001    0.000 {method 'update' of 'dict' objects}
      161    0.000    0.000    0.001    0.000 typing.py:379(_remove_dups_flatten)
      234    0.000    0.000    0.014    0.000 <frozen importlib._bootstrap>:806(module_from_spec)
       12    0.000    0.000    0.000    0.000 {method 'send' of '_socket.socket' objects}
       24    0.000    0.000    0.000    0.000 {method 'write' of '_io.TextIOWrapper' objects}
      248    0.000    0.000    0.001    0.000 events.py:36(__init__)
        1    0.000    0.000    0.000    0.000 idnadata.py:1(<module>)
       72    0.000    0.000    0.001    0.000 _make.py:105(attrib)
      397    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap>:162(__enter__)
      229    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:1619(_get_spec)
     2134    0.000    0.000    0.000    0.000 {method 'endswith' of 'str' objects}
      103    0.000    0.000    0.000    0.000 {built-in method posix.getcwd}
        9    0.000    0.000    0.002    0.000 typing.py:2998(__new__)
      524    0.000    0.000    0.001    0.000 enum.py:78(_is_private)
     1134    0.000    0.000    0.007    0.000 <frozen importlib._bootstrap_external>:145(_path_stat)
       96    0.000    0.000    0.000    0.000 base_events.py:457(create_future)
      585    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:89(_unpack_uint32)
      336    0.000    0.000    0.001    0.000 base_events.py:772(time)
       85    0.000    0.000    0.002    0.000 _compiler.py:514(_compile_info)
      397    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap>:74(__new__)
      350    0.000    0.000    0.002    0.000 typing.py:1307(__init__)
   132/15    0.000    0.000    0.000    0.000 {built-in method _abc._abc_subclasscheck}
        2    0.000    0.000    0.022    0.011 helpers.py:1(<module>)
       85    0.000    0.000    0.019    0.000 _compiler.py:743(compile)
      853    0.000    0.000    0.002    0.000 typing.py:164(_type_convert)
       37    0.000    0.000    0.003    0.000 _make.py:650(__init__)
      227    0.000    0.000    0.002    0.000 base_events.py:823(call_soon)
      511    0.000    0.000    0.001    0.000 <frozen os>:712(__getitem__)
       78    0.000    0.000    0.001    0.000 base_subprocess.py:217(_call)
      317    0.000    0.000    0.001    0.000 typing.py:1358(__getattr__)
  520/518    0.000    0.000    0.001    0.000 {built-in method builtins.max}
      104    0.000    0.000    0.000    0.000 _make.py:2430(__init__)
       70    0.000    0.000    0.000    0.000 {built-in method _abc._abc_init}
       37    0.000    0.000    0.016    0.000 _make.py:1415(wrap)
      677    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:1500(_path_importer_cache)
     1680    0.000    0.000    0.000    0.000 _parser.py:255(match)
      397    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:79(__init__)
       89    0.000    0.000    0.003    0.000 typing.py:1634(__getitem__)
      229    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap_external>:642(_get_cached)
      195    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:697(_classify_pyc)
       74    0.000    0.000    0.002    0.000 base_subprocess.py:227(_pipe_data_received)
       74    0.000    0.000    0.001    0.000 streams.py:493(feed_data)
        1    0.000    0.000    0.000    0.000 client_proto.py:25(ResponseHandler)
      740    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:1222(__enter__)
        1    0.000    0.000    0.000    0.000 sslproto.py:270(__init__)
       85    0.000    0.000    0.012    0.000 _parser.py:970(parse)
      217    0.000    0.000    0.000    0.000 _parser.py:311(_class_escape)
        4    0.000    0.000    0.000    0.000 {method 'get' of '_queue.SimpleQueue' objects}
        6    0.000    0.000    0.001    0.000 typing.py:3142(__new__)
      397    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:82(remove)
      249    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:445(cb)
        1    0.000    0.000    0.000    0.000 glob.py:331(_Globber)
      425    0.000    0.000    0.000    0.000 enum.py:37(_is_descriptor)
        2    0.000    0.000    0.001    0.000 {method 'join' of '_thread._ThreadHandle' objects}
        1    0.000    0.000    0.001    0.001 binary.py:1(<module>)
     1614    0.000    0.000    0.000    0.000 {method 'isupper' of 'str' objects}
        3    0.000    0.000    0.000    0.000 {function socket.close at 0x77a57f588c20}
      527    0.000    0.000    0.000    0.000 <frozen os>:794(encode)
       93    0.000    0.000    0.001    0.000 {method 'set_result' of '_asyncio.Future' objects}
      311    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap_external>:164(_path_isfile)
      277    0.000    0.000    0.000    0.000 {built-in method fromkeys}
      579    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:645(parent)
       37    0.000    0.000    0.000    0.000 _make.py:2094(_attrs_to_init_script)
      780    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:139(<genexpr>)
       49    0.000    0.000    0.000    0.000 _compiler.py:389(_mk_bitmap)
     1123    0.000    0.000    0.000    0.000 {method 'get' of 'mappingproxy' objects}
      249    0.000    0.000    0.004    0.000 <frozen importlib._bootstrap>:416(__enter__)
      337    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap_external>:155(_path_is_mode_type)
      993    0.000    0.000    0.000    0.000 _parser.py:163(__len__)
      195    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:730(_validate_timestamp_pyc)
     1049    0.000    0.000    0.000    0.000 typing.py:1437(__eq__)
      249    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:232(__init__)
        1    0.000    0.000    0.000    0.000 selector_events.py:900(_call_connection_lost)
      562    0.000    0.000    0.000    0.000 enum.py:47(_is_dunder)
       76    0.000    0.000    0.001    0.000 streams.py:468(_wakeup_waiter)
      193    0.000    0.000    0.000    0.000 _compiler.py:214(_compile_charset)
      805    0.000    0.000    0.000    0.000 _parser.py:175(append)
       84    0.000    0.000    0.001    0.000 enum.py:1607(__and__)
       28    0.000    0.000    0.000    0.000 _make.py:1764(_make_repr_script)
      114    0.000    0.000    0.000    0.000 enum.py:1059(_add_member_)
      910    0.000    0.000    0.000    0.000 {method '__exit__' of '_thread.RLock' objects}
      195    0.000    0.000    0.000    0.000 {built-in method _imp._fix_co_filename}
       12    0.000    0.000    0.000    0.000 __init__.py:298(__init__)
   168/84    0.000    0.000    0.090    0.001 <frozen importlib._bootstrap>:1390(_handle_fromlist)
      341    0.000    0.000    0.000    0.000 {built-in method time.monotonic}
      218    0.000    0.000    0.000    0.000 enum.py:764(__delattr__)
      245    0.000    0.000    0.019    0.000 <frozen importlib._bootstrap_external>:1551(find_spec)
      123    0.000    0.000    0.000    0.000 {built-in method _contextvars.copy_context}
      3/0    0.000    0.000    0.000          threading.py:1027(_bootstrap_inner)
        2    0.000    0.000    0.129    0.064 client.py:1(<module>)
       89    0.000    0.000    0.000    0.000 base_events.py:1947(_add_callback)
     1386    0.000    0.000    0.000    0.000 {built-in method _imp.release_lock}
      740    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:1226(__exit__)
      114    0.000    0.000    0.000    0.000 <frozen posixpath>:72(join)
      834    0.000    0.000    0.000    0.000 {built-in method _thread.get_ident}
       87    0.000    0.000    0.004    0.000 typing.py:806(Optional)
      607    0.000    0.000    0.000    0.000 {built-in method from_bytes}
       37    0.000    0.000    0.008    0.000 _make.py:760(_eval_snippets)
     1386    0.000    0.000    0.000    0.000 {built-in method _imp.acquire_lock}
      518    0.000    0.000    0.002    0.000 typing.py:789(<genexpr>)
      650    0.000    0.000    0.000    0.000 _parser.py:292(tell)
        1    0.000    0.000    0.000    0.000 {built-in method _imp.get_frozen_object}
        9    0.000    0.000    0.000    0.000 {built-in method _io.open}
      133    0.000    0.000    0.003    0.000 typing.py:1658(copy_with)
      203    0.000    0.000    0.001    0.000 typing.py:1639(<genexpr>)
       39    0.000    0.000    0.000    0.000 _make.py:1556(_make_hash_script)
       72    0.000    0.000    0.000    0.000 _make.py:2671(__init__)
      848    0.000    0.000    0.000    0.000 <frozen os>:798(decode)
  377/316    0.000    0.000    0.000    0.000 typing.py:1443(__hash__)
       16    0.000    0.000    0.001    0.000 typing.py:1207(_generic_class_getitem)
       79    0.000    0.000    0.000    0.000 typing.py:3794(__getattr__)
    30/26    0.000    0.000    0.000    0.000 {method 'acquire' of '_thread.lock' objects}
  780/556    0.000    0.000    0.000    0.000 {built-in method builtins.hash}
      195    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:674(_check_name_wrapper)
      672    0.000    0.000    0.000    0.000 typing.py:1765(__eq__)
      524    0.000    0.000    0.000    0.000 enum.py:58(_is_sunder)
      510    0.000    0.000    0.000    0.000 {method 'pop' of 'dict' objects}
      336    0.000    0.000    0.000    0.000 enum.py:1589(_get_value)
      314    0.000    0.000    0.000    0.000 _make.py:1177(_add_method_dunders_unsafe)
        1    0.000    0.000    0.038    0.038 subprocess.py:2075(_communicate)
      424    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap>:632(cached)
     1077    0.000    0.000    0.000    0.000 {built-in method builtins.min}
      148    0.000    0.000    0.002    0.000 <frozen importlib._bootstrap>:463(_lock_unlock_module)
      850    0.000    0.000    0.000    0.000 {method 'decode' of 'bytes' objects}
     1065    0.000    0.000    0.000    0.000 {built-in method posix.fspath}
        1    0.000    0.000    0.000    0.000 stringprep.py:1(<module>)
  189/152    0.000    0.000    0.000    0.000 typing.py:1773(__hash__)
       76    0.000    0.000    0.000    0.000 {method 'extend' of 'bytearray' objects}
      249    0.000    0.000    0.000    0.000 {built-in method _imp.is_builtin}
        5    0.000    0.000    0.000    0.000 {method 'read' of '_ssl._SSLSocket' objects}
       74    0.000    0.000    0.001    0.000 subprocess.py:64(pipe_data_received)
        1    0.000    0.000    0.000    0.000 {method 'getall' of 'multidict._multidict.MultiDictProxy' objects}
      129    0.000    0.000    0.001    0.000 enum.py:695(__call__)
       14    0.000    0.000    0.000    0.000 {method 'recv' of '_socket.socket' objects}
      197    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:48(_new_module)
        3    0.000    0.000    0.003    0.001 subprocess.py:1808(_execute_child)
      327    0.000    0.000    0.000    0.000 {method 'popleft' of 'collections.deque' objects}
     1025    0.000    0.000    0.000    0.000 typing.py:1427(<genexpr>)
        3    0.000    0.000    0.002    0.001 dataclasses.py:929(_process_class)
      340    0.000    0.000    0.000    0.000 {method 'append' of 'collections.deque' objects}
      249    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap>:420(__exit__)
       85    0.000    0.000    0.007    0.000 _compiler.py:576(_code)
        1    0.000    0.000    0.004    0.004 hdrs.py:1(<module>)
      114    0.000    0.000    0.001    0.000 parser.py:90(read_regex)
        4    0.000    0.000    0.000    0.000 unix_events.py:197(_make_subprocess_transport)
   100/74    0.000    0.000    0.000    0.000 _compiler.py:439(_get_literal_prefix)
      636    0.000    0.000    0.000    0.000 {method 'setdefault' of 'dict' objects}
      397    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:173(__exit__)
  261/174    0.000    0.000    0.008    0.000 typing.py:574(__getitem__)
      621    0.000    0.000    0.000    0.000 {method 'find' of 'bytearray' objects}
       74    0.000    0.000    0.002    0.000 base_subprocess.py:307(data_received)
       44    0.000    0.000    0.002    0.000 typing.py:1748(__getitem__)
      549    0.000    0.000    0.000    0.000 {method 'pop' of 'list' objects}
        1    0.000    0.000    0.001    0.001 client.py:231(ClientSession)
       37    0.000    0.000    0.001    0.000 _make.py:1937(_make_init_script)
      246    0.000    0.000    0.000    0.000 {built-in method _imp.find_frozen}
      246    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:1128(find_spec)
  627/626    0.000    0.000    0.000    0.000 {method 'encode' of 'str' objects}
        1    0.000    0.000    0.000    0.000 {method 'connect' of '_socket.socket' objects}
      309    0.000    0.000    0.000    0.000 {method 'extend' of 'list' objects}
      454    0.000    0.000    0.000    0.000 {method 'add' of 'set' objects}
       72    0.000    0.000    0.000    0.000 _make.py:2488(from_counting_attr)
      462    0.000    0.000    0.000    0.000 {method 'rfind' of 'str' objects}
        1    0.000    0.000    0.000    0.000 {method '_wrap_bio' of '_ssl._SSLContext' objects}
        1    0.000    0.000    0.003    0.003 multipart.py:1(<module>)
      508    0.000    0.000    0.000    0.000 _parser.py:112(__init__)
        1    0.000    0.000    0.004    0.004 _make.py:1(<module>)
      128    0.000    0.000    0.000    0.000 enum.py:1156(__new__)
      463    0.000    0.000    0.000    0.000 typing.py:1096(__eq__)
       40    0.000    0.000    0.000    0.000 _make.py:1654(_make_eq_script)
    44/28    0.000    0.000    0.095    0.003 {built-in method builtins.__import__}
      240    0.000    0.000    0.000    0.000 {method 'match' of 're.Pattern' objects}
        5    0.000    0.000    0.003    0.001 client.py:1481(__aenter__)
      111    0.000    0.000    0.000    0.000 _parser.py:448(_uniq)
       51    0.000    0.000    0.001    0.000 typing.py:1023(__init__)
      383    0.000    0.000    0.000    0.000 socket.py:79(<lambda>)
        1    0.000    0.000    0.000    0.000 client_proto.py:221(set_response_params)
      385    0.000    0.000    0.000    0.000 socket.py:89(<lambda>)
        1    0.000    0.000    0.000    0.000 client_proto.py:291(data_received)
      404    0.000    0.000    0.000    0.000 {method 'remove' of 'list' objects}
      386    0.000    0.000    0.000    0.000 socket.py:94(<lambda>)
        1    0.000    0.000    0.006    0.006 socket.py:1(<module>)
      241    0.000    0.000    0.000    0.000 typing.py:1122(_is_unpacked_typevartuple)
      195    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:1233(path_stats)
      384    0.000    0.000    0.000    0.000 socket.py:84(<lambda>)
       52    0.000    0.000    0.000    0.000 {built-in method math.ceil}
        1    0.000    0.000    0.005    0.005 validators.py:1(<module>)
       34    0.000    0.000    0.010    0.000 <frozen importlib._bootstrap_external>:1318(create_module)
        3    0.000    0.000    0.000    0.000 {built-in method posix.unlink}
      148    0.000    0.000    0.000    0.000 _parser.py:371(_escape)
       37    0.000    0.000    0.000    0.000 {method 'splitlines' of 'str' objects}
       39    0.000    0.000    0.000    0.000 enum.py:987(_find_data_type_)
      906    0.000    0.000    0.000    0.000 {built-in method builtins.ord}
        9    0.000    0.000    0.006    0.001 lofi_cli.py:83(generate_video)
      166    0.000    0.000    0.000    0.000 typing.py:351(_deduplicate)
        1    0.000    0.000    0.000    0.000 client.py:272(__init__)
        7    0.000    0.000    0.000    0.000 {method 'put' of '_queue.SimpleQueue' objects}
      176    0.000    0.000    0.000    0.000 _make.py:1235(_determine_attrib_eq_order)
        9    0.000    0.000    0.000    0.000 {built-in method posix.pipe}
        8    0.000    0.000    0.000    0.000 enum.py:1459(_missing_)
      424    0.000    0.000    0.000    0.000 <frozen os>:735(__iter__)
      105    0.000    0.000    0.000    0.000 _parser.py:85(opengroup)
       35    0.000    0.000    0.000    0.000 ipaddress.py:1661(_ip_int_from_string)
       24    0.000    0.000    0.000    0.000 typing.py:1256(_generic_init_subclass)
        1    0.000    0.000    0.000    0.000 _url.py:268(URL)
        1    0.000    0.000    0.009    0.009 ssl.py:1(<module>)
       36    0.000    0.000    0.000    0.000 _make.py:1691(_make_order)
        1    0.000    0.000    0.035    0.035 http_parser.py:1(<module>)
      253    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:190(_path_abspath)
       24    0.000    0.000    0.000    0.000 {built-in method time.localtime}
        1    0.000    0.000    0.000    0.000 quoprimime.py:1(<module>)
        2    0.000    0.000    0.000    0.000 connector.py:1011(close)
        4    0.000    0.000    0.005    0.001 selector_events.py:964(_read_ready__get_buffer)
       82    0.000    0.000    0.002    0.000 subprocess.py:171(_read_stream)
      249    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:982(find_spec)
        4    0.000    0.000    0.000    0.000 {method 'recv_into' of '_socket.socket' objects}
       24    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:1487(_path_hooks)
      218    0.000    0.000    0.000    0.000 {built-in method builtins.delattr}
       24    0.000    0.000    0.000    0.000 <frozen zipimport>:68(__init__)
       10    0.000    0.000    0.000    0.000 {method 'release' of '_thread.lock' objects}
       39    0.000    0.000    0.000    0.000 _make.py:1583(append_hash_computation_lines)
       85    0.000    0.000    0.000    0.000 {built-in method _sre.compile}
      398    0.000    0.000    0.000    0.000 {built-in method _weakref._remove_dead_weakref}
      382    0.000    0.000    0.000    0.000 _parser.py:82(groups)
       24    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1598(__init__)
       70    0.000    0.000    0.002    0.000 <frozen abc>:105(__new__)
       19    0.000    0.000    0.000    0.000 enum.py:484(__prepare__)
      181    0.000    0.000    0.000    0.000 _compiler.py:401(_simple)
       28    0.000    0.000    0.000    0.000 functools.py:36(update_wrapper)
      253    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:185(_path_isabs)
        1    0.000    0.000    0.041    0.041 base_events.py:1(<module>)
      114    0.000    0.000    0.000    0.000 parser.py:53(advance)
        1    0.000    0.000    0.000    0.000 {method 'getpeercert' of '_ssl._SSLSocket' objects}
       18    0.000    0.000    0.001    0.000 parser.py:135(parse_binding)
       24    0.000    0.000    0.001    0.000 <frozen importlib._bootstrap_external>:1675(_fill_cache)
       37    0.000    0.000    0.010    0.000 _make.py:778(build_class)
       66    0.000    0.000    0.000    0.000 __init__.py:26(__new__)
        1    0.000    0.000    0.011    0.011 client_reqrep.py:1(<module>)
       24    0.000    0.000    0.000    0.000 __init__.py:699(format)
       19    0.000    0.000    0.000    0.000 typing.py:1715(copy_with)
       37    0.000    0.000    0.008    0.000 _make.py:226(_linecache_and_compile)
        1    0.000    0.000    0.009    0.009 tracing.py:1(<module>)
      424    0.000    0.000    0.000    0.000 <frozen _collections_abc>:873(__iter__)
      114    0.000    0.000    0.000    0.000 __init__.py:270(findall)
        5    0.000    0.000    0.003    0.001 video_generator.py:94(generate_video)
       12    0.000    0.000    0.000    0.000 base_events.py:461(create_task)
        3    0.000    0.000    0.000    0.000 subprocess.py:1298(_close_pipe_fds)
       36    0.000    0.000    0.000    0.000 _make.py:1135(add_order)
       19    0.000    0.000    0.000    0.000 enum.py:1014(_find_new_)
       35    0.000    0.000    0.001    0.000 ipaddress.py:2316(__init__)
      2/1    0.000    0.000   25.106   25.106 lofi_cli.py:1(<module>)
      144    0.000    0.000    0.000    0.000 {built-in method sys.intern}
        1    0.000    0.000    0.006    0.006 _url.py:1(<module>)
       85    0.000    0.000    0.000    0.000 _parser.py:230(__init__)
       31    0.000    0.000    0.000    0.000 {built-in method builtins.sorted}
        1    0.000    0.000    0.002    0.002 cookies.py:1(<module>)
       28    0.000    0.000    0.000    0.000 dataclasses.py:768(_get_field)
      172    0.000    0.000    0.000    0.000 {method 'split' of 'str' objects}
       12    0.000    0.000    0.001    0.000 gettext.py:488(find)
       88    0.000    0.000    0.000    0.000 ipaddress.py:1213(_parse_octet)
       12    0.000    0.000    0.000    0.000 __init__.py:1594(findCaller)
        1    0.000    0.000    0.021    0.021 connector.py:1(<module>)
      314    0.000    0.000    0.000    0.000 {method 'items' of 'dict' objects}
        1    0.000    0.000    0.000    0.000 client_proto.py:110(connection_lost)
       56    0.000    0.000    0.000    0.000 _compiler.py:470(_get_charset_prefix)
        3    0.000    0.000    0.004    0.001 sslproto.py:438(buffer_updated)
      251    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:599(__init__)
       12    0.000    0.000    0.000    0.000 argparse.py:1355(__init__)
        2    0.000    0.000    0.038    0.019 selectors.py:385(select)
        9    0.000    0.000    0.001    0.000 argparse.py:1440(add_argument)
        4    0.000    0.000    0.000    0.000 unix_events.py:499(__init__)
        9    0.000    0.000    0.002    0.000 typing.py:2979(_make_nmtuple)
      296    0.000    0.000    0.000    0.000 {built-in method builtins.issubclass}
       24    0.000    0.000    0.000    0.000 gettext.py:231(_expand_lang)
        1    0.000    0.000    0.002    0.002 tasks.py:1(<module>)
       24    0.000    0.000    0.000    0.000 {built-in method time.strftime}
      255    0.000    0.000    0.000    0.000 {built-in method _thread.allocate_lock}
      578    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:71(_relax_case)
        1    0.000    0.000    0.000    0.000 {built-in method posix.open}
       10    0.000    0.000    0.053    0.005 lofi_cli.py:298(run_cli)
      270    0.000    0.000    0.000    0.000 base_events.py:554(_check_closed)
        1    0.000    0.000    0.002    0.002 sslproto.py:1(<module>)
        1    0.000    0.000    0.002    0.002 cookiejar.py:51(CookieJar)
       23    0.000    0.000    0.000    0.000 _local.py:227(__str__)
        1    0.000    0.000    0.009    0.009 ssl.py:682(create_default_context)
       97    0.000    0.000    0.000    0.000 {method 'cancelled' of '_asyncio.Future' objects}
        1    0.000    0.000    0.004    0.004 request.py:1(<module>)
        2    0.000    0.000    0.001    0.000 streams.py:1(<module>)
       94    0.000    0.000    0.000    0.000 typing.py:2528(get_origin)
       19    0.000    0.000    0.000    0.000 enum.py:346(__init__)
       24    0.000    0.000    0.002    0.000 __init__.py:1139(emit)
       87    0.000    0.000    0.000    0.000 _make.py:1546(_generate_unique_filename)
        3    0.000    0.000    0.003    0.001 subprocess.py:817(__init__)
       92    0.000    0.000    0.000    0.000 {built-in method _sre.unicode_tolower}
      112    0.000    0.000    0.020    0.000 __init__.py:287(compile)
       24    0.000    0.000    0.000    0.000 __init__.py:463(_format)
        1    0.000    0.000    0.000    0.000 base_subprocess.py:99(close)
        3    0.000    0.000    0.000    0.000 {built-in method builtins.dir}
       28    0.000    0.000    0.000    0.000 enum.py:1596(__or__)
       39    0.000    0.000    0.000    0.000 enum.py:946(_get_mixins_)
       13    0.000    0.000    0.000    0.000 {built-in method posix.close}
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1407(send)
        1    0.000    0.000    0.001    0.001 pickle.py:1(<module>)
        2    0.000    0.000    0.002    0.001 subprocess.py:1(<module>)
       36    0.000    0.000    0.000    0.000 _make.py:1043(add_hash)
       15    0.000    0.000    0.000    0.000 __init__.py:1362(getLogger)
       98    0.000    0.000    0.000    0.000 _make.py:2354(_default_init_alias_for)
        1    0.000    0.000    0.001    0.001 connector.py:878(TCPConnector)
       44    0.000    0.000    0.000    0.000 _make.py:290(_is_class_var)
       27    0.000    0.000    0.000    0.000 {built-in method builtins.any}
        4    0.000    0.000    0.000    0.000 {built-in method _ssl.txt2obj}
       37    0.000    0.000    0.000    0.000 _make.py:1304(attrs)
        1    0.000    0.000    0.000    0.000 sslproto.py:571(_on_handshake_complete)
       10    0.000    0.000    0.000    0.000 argparse.py:162(__init__)
        3    0.000    0.000    0.000    0.000 timeouts.py:50(reschedule)
       28    0.000    0.000    0.000    0.000 dataclasses.py:531(_field_init)
        2    0.000    0.000    0.001    0.000 platform.py:1133(_sys_version)
        1    0.000    0.000    0.000    0.000 _compat_pickle.py:1(<module>)
        8    0.000    0.000    0.000    0.000 typing.py:1927(_get_protocol_attrs)
       19    0.000    0.000    0.001    0.000 typing.py:1733(__getitem_inner__)
       36    0.000    0.000    0.000    0.000 _make.py:998(_make_getstate_setstate)
        1    0.000    0.000    0.002    0.002 shutil.py:1(<module>)
       52    0.000    0.000    0.000    0.000 ipaddress.py:1939(__init__)
       93    0.000    0.000    0.000    0.000 subprocess.py:1897(<genexpr>)
        3    0.000    0.000   27.609    9.203 base_events.py:689(run_until_complete)
        1    0.000    0.000    0.001    0.001 <frozen ntpath>:1(<module>)
       24    0.000    0.000    0.002    0.000 __init__.py:1011(handle)
      148    0.000    0.000    0.000    0.000 argparse.py:1409(register)
       37    0.000    0.000    0.001    0.000 _make.py:1058(add_init)
      395    0.000    0.000    0.000    0.000 base_events.py:2060(get_debug)
      208    0.000    0.000    0.000    0.000 _make.py:1244(decide_callable_or_boolean)
       56    0.000    0.000    0.000    0.000 ipaddress.py:533(_split_addr_prefix)
      189    0.000    0.000    0.000    0.000 _parser.py:171(__setitem__)
       15    0.000    0.000    0.000    0.000 __init__.py:1410(_fixupParents)
       36    0.000    0.000    0.000    0.000 _make.py:1121(add_eq)
      397    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:158(__init__)
        2    0.000    0.000    0.002    0.001 _abc.py:1(<module>)
        1    0.000    0.000    0.002    0.002 payload.py:1(<module>)
        2    0.000    0.000    0.002    0.001 base_subprocess.py:15(__init__)
       64    0.000    0.000    0.000    0.000 <frozen _collections_abc>:808(get)
        1    0.000    0.000    0.000    0.000 events.py:221(AbstractEventLoop)
       93    0.000    0.000    0.000    0.000 <frozen os>:844(fsencode)
       13    0.000    0.000    0.000    0.000 __init__.py:43(normalize_encoding)
        6    0.000    0.000    0.000    0.000 events.py:157(cancel)
       26    0.000    0.000    0.001    0.000 typing.py:1719(__getitem__)
       12    0.000    0.000    0.002    0.000 __init__.py:1669(handle)
      180    0.000    0.000    0.000    0.000 _make.py:1138(<genexpr>)
       14    0.000    0.000    0.000    0.000 {method 'sort' of 'list' objects}
        1    0.000    0.000    0.000    0.000 base_events.py:417(BaseEventLoop)
       21    0.000    0.000    0.000    0.000 ipaddress.py:1512(__init__)
        3    0.000    0.000    0.000    0.000 dataclasses.py:610(_init_fn)
        1    0.000    0.000    0.003    0.003 parse.py:1(<module>)
       12    0.000    0.000    0.000    0.000 _local.py:257(_parse_path)
       12    0.000    0.000    0.002    0.000 __init__.py:1728(callHandlers)
        1    0.000    0.000    0.000    0.000 base_events.py:100(_ipaddr_info)
      170    0.000    0.000    0.000    0.000 _compiler.py:573(isstring)
      129    0.000    0.000    0.000    0.000 typing.py:1757(<genexpr>)
        1    0.000    0.000    0.000    0.000 {built-in method posix.scandir}
        1    0.000    0.000    0.004    0.004 main.py:1(<module>)
      105    0.000    0.000    0.001    0.000 _parser.py:97(closegroup)
        1    0.000    0.000    0.001    0.001 client_reqrep.py:280(ClientResponse)
        1    0.000    0.000    0.001    0.001 client_reqrep.py:786(ClientRequest)
        1    0.000    0.000    0.006    0.006 cookiejar.py:1(<module>)
        1    0.000    0.000    0.000    0.000 _local.py:329(parent)
        3    0.000    0.000    0.000    0.000 {function Random.seed at 0x77a57eb75760}
        1    0.000    0.000    0.043    0.043 http.py:1(<module>)
        5    0.000    0.000    0.002    0.000 client.py:719(_connect_and_send_request)
       11    0.000    0.000    0.002    0.000 __init__.py:1512(info)
      2/1    0.000    0.000    2.551    2.551 runners.py:86(run)
       34    0.000    0.000    0.006    0.000 <frozen importlib._bootstrap_external>:1326(exec_module)
        1    0.000    0.000    0.001    0.001 client_middleware_digest_auth.py:1(<module>)
        4    0.000    0.000    0.000    0.000 selector_events.py:289(_remove_reader)
       24    0.000    0.000    0.001    0.000 __init__.py:1131(flush)
        1    0.000    0.000    0.021    0.021 base_protocol.py:1(<module>)
       12    0.000    0.000    0.000    0.000 _local.py:289(drive)
        7    0.000    0.000    0.000    0.000 selectors.py:351(unregister)
       37    0.000    0.000    0.000    0.000 _make.py:1089(add_match_args)
       19    0.000    0.000    0.000    0.000 typing.py:2696(overload)
        2    0.000    0.000    0.016    0.008 connector.py:847(_make_ssl_context)
        5    0.000    0.000    0.000    0.000 threading.py:327(wait)
        3    0.000    0.000    5.104    1.701 base_events.py:678(run_forever)
      249    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:412(__init__)
        1    0.000    0.000    0.001    0.001 random.py:1(<module>)
        1    0.000    0.000    0.000    0.000 client_ws.py:46(ClientWebSocketResponse)
       24    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1716(path_hook_for_FileFinder)
       12    0.000    0.000    0.003    0.000 __init__.py:1643(_log)
      109    0.000    0.000    0.001    0.000 __init__.py:164(match)
       48    0.000    0.000    0.008    0.000 _make.py:212(_compile_and_eval)
       36    0.000    0.000    0.000    0.000 __init__.py:197(_is_internal_frame)
        1    0.000    0.000    0.002    0.002 core.py:1(<module>)
        2    0.000    0.000    0.000    0.000 base_events.py:601(shutdown_default_executor)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:735(json)
        7    0.000    0.000    0.000    0.000 {method 'register' of 'select.epoll' objects}
       19    0.000    0.000    0.000    0.000 enum.py:936(_check_for_existing_members_)
       24    0.000    0.000    0.000    0.000 locale.py:381(normalize)
        3    0.000    0.000    0.000    0.000 <frozen os>:657(get_exec_path)
       85    0.000    0.000    0.000    0.000 _parser.py:954(fix_flags)
        3    0.000    0.000    0.004    0.001 sslproto.py:561(_do_handshake)
        1    0.000    0.000    0.001    0.001 signal.py:1(<module>)
       12    0.000    0.000    0.000    0.000 typing.py:731(Final)
       10    0.000    0.000    0.000    0.000 shutil.py:1439(get_terminal_size)
       13    0.000    0.000    0.000    0.000 threading.py:398(notify)
       47    0.000    0.000    0.000    0.000 _parser.py:264(getwhile)
        6    0.000    0.000    0.000    0.000 base_subprocess.py:179(_connect_pipes)
        1    0.000    0.000    0.000    0.000 unix_events.py:868(AbstractChildWatcher)
       22    0.000    0.000    0.000    0.000 functools.py:504(lru_cache)
      134    0.000    0.000    0.000    0.000 <frozen posixpath>:42(_get_sep)
        4    0.000    0.000    0.000    0.000 lofi_manager.py:130(_calculate_style_score)
        3    0.000    0.000    0.001    0.000 dataclasses.py:470(add_fns_to_class)
        4    0.000    0.000    0.000    0.000 threading.py:641(wait)
       49    0.000    0.000    0.000    0.000 {method 'translate' of 'bytearray' objects}
        4    0.000    0.000    0.000    0.000 {method 'close' of '_io.BufferedReader' objects}
        4    0.000    0.000    0.001    0.000 argparse.py:1765(__init__)
        1    0.000    0.000    0.039    0.039 subprocess.py:512(run)
        3    0.000    0.000    0.000    0.000 sslproto.py:778(_do_read__copied)
      150    0.000    0.000    0.000    0.000 _compiler.py:31(_combine_flags)
       16    0.000    0.000    0.000    0.000 __init__.py:1480(__init__)
       15    0.000    0.000    0.000    0.000 __init__.py:2141(getLogger)
      485    0.000    0.000    0.000    0.000 {built-in method builtins.chr}
        4    0.000    0.000    0.000    0.000 threading.py:955(start)
       12    0.000    0.000    0.000    0.000 __init__.py:1772(isEnabledFor)
        1    0.000    0.000    0.000    0.000 uuid.py:88(UUID)
        4    0.000    0.000    0.000    0.000 argparse.py:2307(_match_arguments_partial)
        6    0.000    0.000    0.000    0.000 contextlib.py:534(callback)
       83    0.000    0.000    0.000    0.000 typing.py:3119(_get_typeddict_qualifiers)
        1    0.000    0.000    0.000    0.000 threading.py:1(<module>)
        5    0.000    0.000    0.002    0.000 client.py:484(_request)
        2    0.000    0.000    0.003    0.002 base.py:1(<module>)
        4    0.000    0.000    0.000    0.000 thread.py:165(submit)
       45    0.000    0.000    0.000    0.000 threading.py:303(__enter__)
        8    0.000    0.000    0.000    0.000 typing.py:2199(__init_subclass__)
      127    0.000    0.000    0.000    0.000 typing.py:1105(__hash__)
        6    0.000    0.000    0.000    0.000 base_events.py:805(call_at)
      142    0.000    0.000    0.000    0.000 {method 'isidentifier' of 'str' objects}
       10    0.000    0.000    0.000    0.000 threading.py:281(__init__)
      410    0.000    0.000    0.000    0.000 enum.py:1217(__init__)
      195    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1184(__init__)
       24    0.000    0.000    0.000    0.000 _make.py:977(add_repr)
        3    0.000    0.000    0.001    0.000 connector.py:1235(_wrap_create_connection)
      183    0.000    0.000    0.000    0.000 {method 'discard' of 'set' objects}
      3/1    0.000    0.000    0.000    0.000 typing.py:1512(_make_substitution)
        8    0.000    0.000    0.000    0.000 sslproto.py:718(_process_outgoing)
        1    0.000    0.000    0.007    0.007 http_writer.py:1(<module>)
        5    0.000    0.000    0.000    0.000 {method 'unregister' of 'select.epoll' objects}
        5    0.000    0.000    0.000    0.000 {built-in method posix.mkdir}
        9    0.000    0.000    0.000    0.000 selectors.py:238(register)
        1    0.000    0.000    0.002    0.002 hashlib.py:1(<module>)
        1    0.000    0.000    0.000    0.000 unix_events.py:58(_UnixSelectorEventLoop)
        8    0.000    0.000    0.000    0.000 base_events.py:876(call_soon_threadsafe)
        2    0.000    0.000    0.000    0.000 tasks.py:784(gather)
        4    0.000    0.000    0.001    0.000 manager.py:84(get_audio_data)
        1    0.000    0.000    0.002    0.002 main.py:89(set_as_environment_variables)
      2/1    0.000    0.000    0.001    0.001 argparse.py:1948(_parse_known_args)
        3    0.000    0.000    0.000    0.000 socket.py:221(__init__)
        1    0.000    0.000    0.002    0.002 unix_events.py:1(<module>)
       56    0.000    0.000    0.000    0.000 ipaddress.py:156(_split_optional_netmask)
        1    0.000    0.000    0.001    0.001 traceback.py:1(<module>)
        1    0.000    0.000    0.090    0.090 unix_events.py:1439(_do_waitpid)
        3    0.000    0.000    0.000    0.000 contextlib.py:571(__exit__)
        2    0.000    0.000    0.000    0.000 client_reqrep.py:521(start)
        7    0.000    0.000    0.000    0.000 selector_events.py:129(_read_from_self)
        2    0.000    0.000    0.000    0.000 {method 'toordinal' of 'datetime.date' objects}
        6    0.000    0.000    0.000    0.000 selector_events.py:274(_add_reader)
        2    0.000    0.000    0.008    0.004 parser.py:1(<module>)
        3    0.000    0.000    0.000    0.000 {method 'write' of '_ssl.MemoryBIO' objects}
       76    0.000    0.000    0.000    0.000 signal.py:9(<lambda>)
        1    0.000    0.000    0.003    0.003 _cookie_helpers.py:1(<module>)
        1    0.000    0.000    0.000    0.000 tcp_helpers.py:24(tcp_nodelay)
        2    0.000    0.000    0.000    0.000 glob.py:424(select_wildcard)
        5    0.000    0.000    0.000    0.000 typing.py:812(Literal)
       19    0.000    0.000    0.000    0.000 _parser.py:273(getuntil)
       15    0.000    0.000    0.000    0.000 _local.py:117(__init__)
        4    0.000    0.000    0.000    0.000 _base.py:537(set_result)
        2    0.000    0.000    0.000    0.000 selector_events.py:857(close)
        2    0.000    0.000    0.000    0.000 subprocess.py:40(connection_made)
        4    0.000    0.000    0.000    0.000 futures.py:342(_copy_future_state)
       39    0.000    0.000    0.000    0.000 {method 'format' of 'str' objects}
        8    0.000    0.000    0.000    0.000 typing.py:2054(__new__)
        1    0.000    0.000    0.000    0.000 {method 'disable' of '_lsprof.Profiler' objects}
       15    0.000    0.000    0.000    0.000 _local.py:498(__init__)
      123    0.000    0.000    0.000    0.000 {method '__contains__' of 'frozenset' objects}
        3    0.000    0.000    0.001    0.000 video_generator.py:317(_generate_visual_background)
       11    0.000    0.000    0.000    0.000 _abc.py:130(with_segments)
       14    0.000    0.000    0.000    0.000 types.py:334(__getattr__)
       21    0.000    0.000    0.000    0.000 _weakrefset.py:85(add)
        1    0.000    0.000    0.006    0.006 abc.py:1(<module>)
        6    0.000    0.000    0.000    0.000 _policybase.py:94(_append_doc)
       13    0.000    0.000    0.000    0.000 functools.py:544(decorating_function)
       76    0.000    0.000    0.000    0.000 streams.py:480(_maybe_resume_transport)
        2    0.000    0.000    0.000    0.000 ssl.py:504(set_alpn_protocols)
        1    0.000    0.000    0.001    0.001 bz2.py:1(<module>)
        1    0.000    0.000    0.001    0.001 client_ws.py:1(<module>)
      185    0.000    0.000    0.000    0.000 _make.py:1278(_determine_whether_to_implement)
        1    0.000    0.000    0.000    0.000 manager.py:110(MusicSourceManager)
   132/15    0.000    0.000    0.000    0.000 <frozen abc>:121(__subclasscheck__)
        1    0.000    0.000    0.000    0.000 decoder.py:351(raw_decode)
      114    0.000    0.000    0.000    0.000 {method 'findall' of 're.Pattern' objects}
        1    0.000    0.000    0.001    0.001 uuid.py:1(<module>)
      250    0.000    0.000    0.000    0.000 {built-in method builtins.callable}
       16    0.000    0.000    0.000    0.000 _weakrefset.py:39(_remove)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:822(__init__)
        4    0.000    0.000    0.000    0.000 selector_events.py:1047(write)
        4    0.000    0.000    0.000    0.000 futures.py:362(_chain_future)
        5    0.000    0.000    0.003    0.001 freesound.py:40(_make_request)
      100    0.000    0.000    0.000    0.000 typing.py:553(__repr__)
        4    0.000    0.000    0.001    0.000 base_events.py:887(run_in_executor)
      195    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1017(create_module)
       93    0.000    0.000    0.000    0.000 utils.py:40(_make_delegate_method)
        4    0.000    0.000    0.002    0.000 connector.py:1488(_create_direct_connection)
        3    0.000    0.000    0.001    0.000 video_generator.py:325(_create_animated_background)
        3    0.000    0.000    0.001    0.000 utils.py:1(<module>)
        4    0.000    0.000    0.002    0.000 connector.py:592(connect)
        2    0.000    0.000    0.000    0.000 selectors.py:336(__init__)
        1    0.000    0.000    0.000    0.000 runners.py:150(_on_sigint)
        1    0.000    0.000    0.001    0.001 base64.py:1(<module>)
      111    0.000    0.000    0.000    0.000 _make.py:1771(<genexpr>)
        9    0.000    0.000    0.000    0.000 selectors.py:340(register)
        1    0.000    0.000    0.000    0.000 connector.py:415(_cleanup)
       47    0.000    0.000    0.000    0.000 ipaddress.py:1774(_parse_hextet)
        1    0.000    0.000    0.002    0.002 events.py:1(<module>)
        3    0.000    0.000    0.000    0.000 subprocess.py:1704(_get_handles)
       38    0.000    0.000    0.000    0.000 base_futures.py:13(isfuture)
        3    0.000    0.000    0.000    0.000 sslproto.py:727(_do_read)
        1    0.000    0.000    0.002    0.002 main.py:233(resolve_variables)
        1    0.000    0.000    0.001    0.001 typedefs.py:1(<module>)
        1    0.000    0.000    0.003    0.003 tempfile.py:1(<module>)
        1    0.000    0.000    0.001    0.001 calendar.py:1(<module>)
       54    0.000    0.000    0.000    0.000 enum.py:199(__get__)
       10    0.000    0.000    0.000    0.000 utils.py:5(cls_builder)
        1    0.000    0.000    0.001    0.001 ipaddress.py:2392(_IPv6Constants)
        1    0.000    0.000    0.001    0.001 selector_events.py:75(_make_ssl_transport)
       36    0.000    0.000    0.000    0.000 utils.py:55(_make_proxy_property)
        4    0.000    0.000    0.005    0.001 selector_events.py:961(_read_ready)
       31    0.000    0.000    0.000    0.000 ipaddress.py:1286(__init__)
        1    0.000    0.000    0.000    0.000 encoder.py:1(<module>)
        1    0.000    0.000    0.000    0.000 connector.py:272(__init__)
        1    0.000    0.000    0.000    0.000 helpers.py:506(_weakref_handle)
       35    0.000    0.000    0.000    0.000 ipaddress.py:1641(_make_netmask)
       12    0.000    0.000    0.000    0.000 __init__.py:1628(makeRecord)
        1    0.000    0.000    0.001    0.001 selectors.py:1(<module>)
       45    0.000    0.000    0.000    0.000 threading.py:306(__exit__)
        5    0.000    0.000    0.000    0.000 functools.py:189(total_ordering)
        2    0.000    0.000    0.000    0.000 events.py:840(get_child_watcher)
        1    0.000    0.000    0.001    0.001 timeouts.py:1(<module>)
        1    0.000    0.000    0.001    0.001 thread.py:1(<module>)
       29    0.000    0.000    0.000    0.000 selectors.py:21(_fileobj_to_fd)
        1    0.000    0.000    0.000    0.000 multipart.py:259(BodyPartReader)
        2    0.000    0.000    0.000    0.000 functools.py:826(singledispatch)
        1    0.000    0.000    0.000    0.000 _colorize.py:1(<module>)
      238    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:653(has_location)
        4    0.000    0.000    0.000    0.000 futures.py:391(_call_set_state)
        2    0.000    0.000    0.003    0.001 _compat.py:1(<module>)
        1    0.000    0.000    0.000    0.000 subprocess.py:759(Popen)
        1    0.000    0.000    0.000    0.000 connector.py:770(_release)
        1    0.000    0.000    0.000    0.000 connector.py:919(__init__)
      156    0.000    0.000    0.000    0.000 _compiler.py:431(_get_iscased)
        1    0.000    0.000    0.000    0.000 sslproto.py:393(connection_lost)
       10    0.000    0.000    0.000    0.000 tasks.py:723(ensure_future)
       11    0.000    0.000    0.000    0.000 events.py:73(cancel)
       13    0.000    0.000    0.000    0.000 <frozen posixpath>:166(basename)
        2    0.000    0.000    0.000    0.000 connector.py:1138(_resolve_host_with_throttle)
        1    0.000    0.000    0.150    0.150 manager.py:1(<module>)
       12    0.000    0.000    0.001    0.000 gettext.py:528(translation)
        1    0.000    0.000    0.000    0.000 {built-in method _socket.socketpair}
       44    0.000    0.000    0.000    0.000 _make.py:422(<genexpr>)
       24    0.000    0.000    0.000    0.000 __init__.py:988(format)
        2    0.000    0.000    0.000    0.000 freesound.py:100(_parse_track)
        4    0.000    0.000    0.000    0.000 futures.py:406(wrap_future)
      104    0.000    0.000    0.000    0.000 enum.py:69(_is_internal_class)
       85    0.000    0.000    0.000    0.000 _parser.py:76(__init__)
        1    0.000    0.000    0.000    0.000 abc.py:39(AbstractRouter)
        1    0.000    0.000    0.000    0.000 client_proto.py:89(close)
      192    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1604(<genexpr>)
        1    0.000    0.000    0.047    0.047 lofi_cli.py:40(setup)
        1    0.000    0.000    0.004    0.004 _quoters.py:1(<module>)
      169    0.000    0.000    0.000    0.000 enum.py:921(<lambda>)
        2    0.000    0.000    0.000    0.000 {method 'close' of '_io.FileIO' objects}
        1    0.000    0.000    0.001    0.001 client_exceptions.py:1(<module>)
        1    0.000    0.000    0.000    0.000 <frozen os>:44(_get_exports_list)
        1    0.000    0.000    0.000    0.000 selector_events.py:673(_sock_connect_cb)
      126    0.000    0.000    0.000    0.000 _make.py:1559(<genexpr>)
        1    0.000    0.000    0.001    0.001 compression_utils.py:1(<module>)
        1    0.000    0.000    0.000    0.000 pickle.py:1180(_Unpickler)
       10    0.000    0.000    0.000    0.000 {built-in method posix.get_terminal_size}
        1    0.000    0.000    0.001    0.001 platform.py:1(<module>)
        5    0.000    0.000    0.003    0.001 freesound.py:142(search)
        4    0.000    0.000    0.000    0.000 {built-in method posix.fstat}
        2    0.000    0.000    0.000    0.000 lofi_manager.py:46(find_lofi_for_style)
        1    0.000    0.000    0.000    0.000 lofi_manager.py:17(LoFiMusicManager)
        1    0.000    0.000    0.000    0.000 base_events.py:419(__init__)
       37    0.000    0.000    0.000    0.000 _compat.py:28(_get_annotations)
        1    0.000    0.000    0.005    0.005 _query.py:1(<module>)
        1    0.000    0.000    0.000    0.000 {built-in method posix.write}
      3/0    0.000    0.000    0.000          threading.py:983(run)
        6    0.000    0.000    0.000    0.000 tasks.py:820(_done_callback)
       12    0.000    0.000    0.000    0.000 <frozen posixpath>:117(splitext)
       37    0.000    0.000    0.000    0.000 _make.py:1074(_attach_init)
      195    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1209(get_filename)
      127    0.000    0.000    0.000    0.000 ssl.py:125(<lambda>)
       11    0.000    0.000    0.000    0.000 functools.py:867(_is_union_type)
       14    0.000    0.000    0.000    0.000 hashlib.py:126(__get_openssl_constructor)
       90    0.000    0.000    0.000    0.000 {built-in method _sre.unicode_iscased}
       61    0.000    0.000    0.000    0.000 {method 'replace' of 'str' objects}
      106    0.000    0.000    0.000    0.000 {method 'lstrip' of 'str' objects}
       37    0.000    0.000    0.000    0.000 _make.py:1083(add_replace)
        9    0.000    0.000    0.000    0.000 socket.py:100(_intenum_converter)
      168    0.000    0.000    0.000    0.000 {method 'isascii' of 'str' objects}
        1    0.000    0.000    0.000    0.000 client.py:478(_build_url)
        1    0.000    0.000    0.000    0.000 connector.py:249(BaseConnector)
       28    0.000    0.000    0.000    0.000 enum.py:1439(_iter_member_by_value_)
       22    0.000    0.000    0.000    0.000 functools.py:878(register)
        1    0.000    0.000    0.011    0.011 socket.py:960(getaddrinfo)
       22    0.000    0.000    0.000    0.000 ipaddress.py:1187(_ip_int_from_string)
        1    0.000    0.000    0.000    0.000 warnings.py:1(<module>)
      127    0.000    0.000    0.000    0.000 ssl.py:140(<lambda>)
       24    0.000    0.000    0.000    0.000 __init__.py:631(formatTime)
        1    0.000    0.000    0.000    0.000 ssl.py:978(SSLSocket)
        1    0.000    0.000    0.000    0.000 client_proto.py:1(<module>)
        1    0.000    0.000    0.007    0.007 _base.py:1(<module>)
      113    0.000    0.000    0.000    0.000 {method 'lower' of 'str' objects}
        8    0.000    0.000    0.000    0.000 argparse.py:1673(__init__)
      127    0.000    0.000    0.000    0.000 ssl.py:150(<lambda>)
      122    0.000    0.000    0.000    0.000 __init__.py:432(<genexpr>)
        1    0.000    0.000    0.002    0.002 _local.py:1(<module>)
        1    0.000    0.000    0.152    0.152 lofi_manager.py:1(<module>)
        1    0.000    0.000    0.000    0.000 payload.py:151(Payload)
       12    0.000    0.000    0.000    0.000 locale.py:347(_replace_encoding)
      127    0.000    0.000    0.000    0.000 ssl.py:135(<lambda>)
        6    0.000    0.000    0.000    0.000 _url.py:348(__new__)
        1    0.000    0.000    0.000    0.000 sslproto.py:263(SSLProtocol)
        8    0.000    0.000    0.000    0.000 selector_events.py:141(_write_to_self)
       14    0.000    0.000    0.000    0.000 typing.py:1599(__mro_entries__)
        1    0.000    0.000    0.000    0.000 selector_events.py:1(<module>)
        1    0.000    0.000    0.000    0.000 multipart.py:597(MultipartReader)
       18    0.000    0.000    0.000    0.000 _compiler.py:410(_generate_overlap_table)
       13    0.000    0.000    0.000    0.000 _local.py:277(_raw_path)
       37    0.000    0.000    0.000    0.000 _make.py:347(_collect_base_attrs_broken)
      127    0.000    0.000    0.000    0.000 ssl.py:145(<lambda>)
        1    0.000    0.000    0.000    0.000 selector_events.py:99(close)
      127    0.000    0.000    0.000    0.000 ssl.py:130(<lambda>)
        1    0.000    0.000    0.000    0.000 _local.py:581(glob)
        1    0.000    0.000    0.000    0.000 _policybase.py:99(_extend_docstrings)
        1    0.000    0.000    0.000    0.000 helpers.py:843(ChainMapProxy)
        2    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_md5}
        8    0.000    0.000    0.000    0.000 base_events.py:1686(connect_read_pipe)
        1    0.000    0.000    0.000    0.000 selector_events.py:769(__init__)
        1    0.000    0.000    0.000    0.000 streams.py:101(StreamReader)
        1    0.000    0.000    0.000    0.000 _parse.py:1(<module>)
        4    0.000    0.000    0.000    0.000 sslproto.py:429(get_buffer)
       29    0.000    0.000    0.000    0.000 selectors.py:219(_fileobj_lookup)
        1    0.000    0.000    0.000    0.000 _base.py:111(_AllCompletedWaiter)
        2    0.000    0.000    0.002    0.001 unix_events.py:846(_start)
       26    0.000    0.000    0.000    0.000 ipaddress.py:474(_prefix_from_prefix_string)
       13    0.000    0.000    0.000    0.000 selectors.py:69(get)
        1    0.000    0.000    0.002    0.002 header.py:1(<module>)
        1    0.000    0.000    0.000    0.000 futures.py:30(Future)
        5    0.000    0.000    0.003    0.001 manager.py:155(_search_source_safe)
      4/1    0.000    0.000    0.000    0.000 {method 'cancel' of '_asyncio.Task' objects}
        3    0.000    0.000    0.000    0.000 {built-in method _imp.create_builtin}
       13    0.000    0.000    0.000    0.000 {built-in method posix.getpid}
        4    0.000    0.000    0.000    0.000 inspect.py:176(get_annotations)
        1    0.000    0.000    0.000    0.000 {method 'shutdown' of '_ssl._SSLSocket' objects}
        4    0.000    0.000    0.000    0.000 subprocess.py:218(create_subprocess_exec)
        1    0.000    0.000    0.000    0.000 sslproto.py:82(_SSLProtocolTransport)
        1    0.000    0.000    0.000    0.000 _local.py:59(PurePath)
        1    0.000    0.000    0.001    0.001 glob.py:1(<module>)
      100    0.000    0.000    0.000    0.000 {method 'find' of 'str' objects}
        1    0.000    0.000    0.000    0.000 http_writer.py:210(write_headers)
        1    0.000    0.000    0.001    0.001 models.py:1(<module>)
       31    0.000    0.000    0.000    0.000 dataclasses.py:1172(<genexpr>)
        1    0.000    0.000    0.000    0.000 multipart.py:803(MultipartWriter)
        6    0.000    0.000    0.000    0.000 argparse.py:1586(_get_optional_kwargs)
        4    0.000    0.000    0.000    0.000 threading.py:472(acquire)
       19    0.000    0.000    0.000    0.000 enum.py:965(_find_data_repr_)
        8    0.000    0.000    0.000    0.000 sslproto.py:823(_control_app_writing)
        2    0.000    0.000    0.000    0.000 weakref.py:104(__init__)
        1    0.000    0.000    0.003    0.003 __init__.py:71(search_function)
        4    0.000    0.000    0.000    0.000 uuid.py:142(__init__)
       24    0.000    0.000    0.000    0.000 __init__.py:677(usesTime)
        1    0.000    0.000    0.000    0.000 main.py:276(find_dotenv)
        1    0.000    0.000   25.058   25.058 runners.py:64(close)
        1    0.000    0.000    0.000    0.000 writer.py:1(<module>)
        1    0.000    0.000    0.000    0.000 message.py:135(Message)
      117    0.000    0.000    0.000    0.000 {method 'groups' of 're.Match' objects}
       74    0.000    0.000    0.000    0.000 _make.py:307(_has_own_attribute)
        3    0.000    0.000    0.000    0.000 random.py:128(seed)
       17    0.000    0.000    0.000    0.000 _local.py:166(__fspath__)
       14    0.000    0.000    0.000    0.000 {built-in method _abc._abc_instancecheck}
        1    0.000    0.000    0.000    0.000 selector_events.py:646(_sock_connect)
        1    0.000    0.000    0.000    0.000 mixins.py:1(<module>)
       21    0.000    0.000    0.000    0.000 ipaddress.py:1161(_make_netmask)
        2    0.000    0.000    0.000    0.000 selector_events.py:323(_remove_writer)
        4    0.000    0.000    0.000    0.000 subprocess.py:188(communicate)
        1    0.000    0.000    0.000    0.000 video_generator.py:19(LoFiVideoGenerator)
        1    0.000    0.000    0.000    0.000 inspect.py:2487(_signature_from_callable)
        1    0.000    0.000    0.000    0.000 futures.py:1(<module>)
       36    0.000    0.000    0.000    0.000 _make.py:1051(attach_hash)
        9    0.000    0.000    0.000    0.000 dataclasses.py:437(add_fn)
      2/1    0.000    0.000    0.001    0.001 argparse.py:1910(_parse_known_args2)
       10    0.000    0.000    0.000    0.000 argparse.py:1501(_add_action)
       62    0.000    0.000    0.000    0.000 {method 'items' of 'mappingproxy' objects}
       12    0.000    0.000    0.000    0.000 payload.py:133(register)
        4    0.000    0.000    0.000    0.000 contextlib.py:276(contextmanager)
        3    0.000    0.000    0.000    0.000 base_events.py:181(_run_until_complete_cb)
        1    0.000    0.000    0.038    0.038 subprocess.py:1178(communicate)
        9    0.000    0.000    0.001    0.000 main.py:83(parse)
       19    0.000    0.000    0.000    0.000 <frozen _collections_abc>:108(_check_methods)
        6    0.000    0.000    0.000    0.000 events.py:113(__init__)
        1    0.000    0.000    0.002    0.002 decoder.py:1(<module>)
        2    0.000    0.000    0.000    0.000 resolver.py:37(resolve)
       28    0.000    0.000    0.000    0.000 dataclasses.py:383(field)
        6    0.000    0.000    0.000    0.000 typing.py:580(__getitem__)
        1    0.000    0.000    0.000    0.000 inspect.py:2383(_signature_from_function)
       28    0.000    0.000    0.000    0.000 {method '__enter__' of '_thread.RLock' objects}
        9    0.000    0.000    0.000    0.000 argparse.py:564(_format_args)
        1    0.000    0.000    0.000    0.000 _encoded_words.py:1(<module>)
        3    0.000    0.000    0.000    0.000 video_generator.py:400(_combine_audio_visual)
      114    0.000    0.000    0.000    0.000 {method 'start' of 're.Match' objects}
        1    0.000    0.000    0.001    0.001 selector_events.py:59(__init__)
        4    0.000    0.000    0.000    0.000 contextlib.py:303(helper)
       15    0.000    0.000    0.002    0.000 parser.py:7(make_regex)
        2    0.000    0.000    0.000    0.000 {built-in method _warnings.warn}
        5    0.000    0.000    0.000    0.000 threading.py:620(set)
        9    0.000    0.000    0.000    0.000 threading.py:428(notify_all)
        1    0.000    0.000    0.001    0.001 charset.py:1(<module>)
        1    0.000    0.000    0.000    0.000 tempfile.py:685(SpooledTemporaryFile)
        2    0.000    0.000    0.000    0.000 helpers.py:647(__enter__)
        1    0.000    0.000    0.001    0.001 resolver.py:1(<module>)
        1    0.000    0.000    0.000    0.000 __init__.py:2016(basicConfig)
        1    0.000    0.000    0.000    0.000 _local.py:482(Path)
      109    0.000    0.000    0.000    0.000 _make.py:459(<genexpr>)
        1    0.000    0.000    0.039    0.039 video_generator.py:82(check_dependencies)
       12    0.000    0.000    0.000    0.000 <frozen genericpath>:157(_splitext)
      116    0.000    0.000    0.000    0.000 {method 'isdigit' of 'str' objects}
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:662(spec_from_loader)
       39    0.000    0.000    0.000    0.000 {method 'values' of 'mappingproxy' objects}
        2    0.000    0.000    0.000    0.000 connector.py:1047(_resolve_host)
        1    0.000    0.000    0.005    0.005 feedparser.py:1(<module>)
        4    0.000    0.000    0.000    0.000 {method 'search' of 're.Pattern' objects}
        1    0.000    0.000    0.134    0.134 freesound.py:1(<module>)
        7    0.000    0.000    0.000    0.000 <frozen posixpath>:176(dirname)
        2    0.000    0.000    0.000    0.000 client.py:1314(close)
      109    0.000    0.000    0.000    0.000 _make.py:1090(<genexpr>)
        1    0.000    0.000    0.001    0.001 impl.py:1(<module>)
      119    0.000    0.000    0.000    0.000 {method 'end' of 're.Match' objects}
        1    0.000    0.000    0.000    0.000 formdata.py:1(<module>)
        1    0.000    0.000    0.001    0.001 client.py:170(_RequestOptions)
        8    0.000    0.000    0.000    0.000 typing.py:2073(__init__)
       16    0.000    0.000    0.000    0.000 variables.py:70(parse_variables)
        3    0.000    0.000    0.000    0.000 base_events.py:642(_run_forever_setup)
        1    0.000    0.000    0.000    0.000 payload.py:466(IOBasePayload)
       90    0.000    0.000    0.000    0.000 {method 'startswith' of 'bytes' objects}
        2    0.000    0.000    0.000    0.000 selector_events.py:621(sock_connect)
        1    0.000    0.000    0.000    0.000 mimetypes.py:1(<module>)
        9    0.000    0.000    0.000    0.000 {method 'read' of '_ssl.MemoryBIO' objects}
        2    0.000    0.000    0.000    0.000 sslproto.py:648(_do_shutdown)
        1    0.000    0.000    0.000    0.000 _abc.py:403(PathBase)
       18    0.000    0.000    0.000    0.000 parser.py:74(get_marked)
        8    0.000    0.000    0.000    0.000 utils.py:23(cls_builder)
       13    0.000    0.000    0.000    0.000 argparse.py:803(__init__)
        1    0.000    0.000    0.000    0.000 helpers.py:320(parse_mimetype)
        3    0.000    0.000    0.000    0.000 threading.py:1049(_delete)
        1    0.000    0.000    0.000    0.000 _url.py:144(encode_url)
        1    0.000    0.000    0.001    0.001 ipaddress.py:1(<module>)
       11    0.000    0.000    0.000    0.000 _local.py:237(_format_parsed_parts)
        4    0.000    0.000    0.000    0.000 _make.py:1747(_add_eq)
        1    0.000    0.000    0.000    0.000 argparse.py:1(<module>)
        5    0.000    0.000    0.000    0.000 warnings.py:488(__enter__)
       19    0.000    0.000    0.001    0.000 parser.py:172(parse_stream)
        1    0.000    0.000    0.000    0.000 connector.py:144(Connection)
       28    0.000    0.000    0.000    0.000 enum.py:115(_iter_bits_lsb)
       36    0.000    0.000    0.000    0.000 _make.py:1126(_attach_eq)
      4/2    0.000    0.000    0.001    0.000 argparse.py:2120(consume_positionals)
        1    0.000    0.000    0.000    0.000 tempfile.py:183(_get_default_tempdir)
        1    0.000    0.000    0.000    0.000 lzma.py:1(<module>)
        3    0.000    0.000    0.001    0.000 argparse.py:1193(add_parser)
        1    0.000    0.000    0.000    0.000 mimetypes.py:426(_default_mime_types)
        2    0.000    0.000    0.000    0.000 lofi_manager.py:90(_is_lofi_track)
       40    0.000    0.000    0.000    0.000 enum.py:1312(__hash__)
        2    0.000    0.000    0.000    0.000 idna.py:179(encode)
        2    0.000    0.000    0.001    0.000 base_events.py:1205(_create_connection_transport)
        5    0.000    0.000    0.000    0.000 ssl.py:859(read)
        1    0.000    0.000    0.000    0.000 base_protocol.py:66(connection_made)
        3    0.000    0.000    0.000    0.000 typing.py:1623(__init__)
      4/1    0.000    0.000    0.000    0.000 argparse.py:1996(take_action)
        1    0.000    0.000    0.000    0.000 runners.py:1(<module>)
        1    0.000    0.000    0.000    0.000 _query.py:40(get_str_query_from_sequence_iterable)
        1    0.000    0.000    0.005    0.005 http_websocket.py:1(<module>)
       36    0.000    0.000    0.000    0.000 __init__.py:840(filter)
        4    0.000    0.000    0.000    0.000 typing.py:1326(__mro_entries__)
       38    0.000    0.000    0.000    0.000 <frozen posixpath>:53(normcase)
        1    0.000    0.000    0.000    0.000 pickle.py:404(_Pickler)
      108    0.000    0.000    0.000    0.000 _make.py:1003(<genexpr>)
      2/1    0.000    0.000   25.058   25.058 runners.py:160(run)
       12    0.000    0.000    0.000    0.000 {built-in method builtins.next}
       54    0.000    0.000    0.000    0.000 enum.py:1337(value)
        1    0.000    0.000    0.000    0.000 tempfile.py:153(__next__)
        1    0.000    0.000    0.000    0.000 __init__.py:1859(LoggerAdapter)
        1    0.000    0.000    0.001    0.001 runners.py:57(__enter__)
        1    0.000    0.000    0.000    0.000 http_writer.py:52(StreamWriter)
       90    0.000    0.000    0.000    0.000 {method 'endswith' of 'bytes' objects}
        1    0.000    0.000    0.011    0.011 client_middlewares.py:1(<module>)
        1    0.000    0.000    0.001    0.001 locks.py:1(<module>)
        1    0.000    0.000    0.000    0.000 thread.py:70(_worker)
       18    0.000    0.000    0.000    0.000 threading.py:1429(current_thread)
        1    0.000    0.000    0.000    0.000 base_events.py:275(Server)
        1    0.000    0.000    0.000    0.000 writer.py:35(WebSocketWriter)
      109    0.000    0.000    0.000    0.000 _make.py:681(<genexpr>)
        1    0.000    0.000    0.000    0.000 sslproto.py:808(_call_eof_received)
       12    0.000    0.000    0.001    0.000 __init__.py:1251(emit)
       26    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:169(_path_isdir)
        1    0.000    0.000    0.000    0.000 temptypes.py:1(<module>)
        2    0.000    0.000    0.000    0.000 {method 'close' of 'select.epoll' objects}
        1    0.000    0.000    0.000    0.000 selector_events.py:1026(_read_ready__on_eof)
       35    0.000    0.000    0.000    0.000 ipaddress.py:1904(_split_scope_id)
        1    0.000    0.000    0.000    0.000 connector.py:497(_close)
        1    0.000    0.000    0.000    0.000 client.py:819(HTTPConnection)
        1    0.000    0.000    0.001    0.001 scanner.py:1(<module>)
        1    0.000    0.000    0.000    0.000 tasks.py:44(all_tasks)
        1    0.000    0.000    0.000    0.000 __init__.py:299(loads)
        6    0.000    0.000    0.000    0.000 {built-in method _heapq.heappush}
        1    0.000    0.000    0.000    0.000 variables.py:18(Atom)
        1    0.000    0.000    0.000    0.000 payload_streamer.py:1(<module>)
        3    0.000    0.000    0.000    0.000 subprocess.py:2034(_wait)
        1    0.000    0.000    0.000    0.000 subprocess.py:701(_use_posix_spawn)
       30    0.000    0.000    0.000    0.000 {method 'setter' of 'property' objects}
       24    0.000    0.000    0.000    0.000 __init__.py:1826(_is_disabled)
        1    0.000    0.000    0.001    0.001 queue.py:1(<module>)
        5    0.000    0.000    0.000    0.000 _weakrefset.py:37(__init__)
        3    0.000    0.000    0.000    0.000 weakref.py:194(get)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:570(_response_eof)
      3/1    0.000    0.000    0.000    0.000 typing.py:1452(__getitem__)
        1    0.000    0.000    0.000    0.000 base_futures.py:1(<module>)
       24    0.000    0.000    0.000    0.000 _make.py:980(_attach_repr)
        1    0.000    0.000    0.000    0.000 cookies.py:236(Morsel)
        3    0.000    0.000    0.000    0.000 timeouts.py:97(__aexit__)
        1    0.000    0.000    0.000    0.000 ssl.py:422(SSLContext)
        2    0.000    0.000    0.000    0.000 subprocess.py:21(__init__)
        8    0.000    0.000    0.000    0.000 typing.py:1813(__hash__)
       57    0.000    0.000    0.000    0.000 _make.py:2006(_setattr)
       24    0.000    0.000    0.000    0.000 __init__.py:455(usesTime)
        3    0.000    0.000    0.000    0.000 {method 'translate' of 'str' objects}
        9    0.000    0.000    0.000    0.000 parse.py:845(__getattr__)
        4    0.000    0.000    0.000    0.000 thread.py:184(_adjust_thread_count)
        3    0.000    0.000    0.000    0.000 socket.py:497(_real_close)
        2    0.000    0.000    0.000    0.000 platform.py:973(uname)
       12    0.000    0.000    0.001    0.000 gettext.py:587(dgettext)
        1    0.000    0.000    0.000    0.000 ipaddress.py:671(_BaseNetwork)
        8    0.000    0.000    0.000    0.000 argparse.py:1491(add_argument_group)
        1    0.000    0.000    0.001    0.001 message.py:1(<module>)
        1    0.000    0.000    0.000    0.000 client.py:1254(get)
        1    0.000    0.000    0.000    0.000 random.py:295(randrange)
        4    0.000    0.000    0.000    0.000 base_events.py:1767(subprocess_exec)
        7    0.000    0.000    0.000    0.000 selectors.py:251(unregister)
        2    0.000    0.000    0.001    0.000 threading.py:1058(join)
        3    0.000    0.000    0.000    0.000 timeouts.py:85(__aenter__)
        1    0.000    0.000    0.000    0.000 main.py:34(DotEnv)
        2    0.000    0.000    0.000    0.000 exceptions.py:1(<module>)
        5    0.000    0.000    0.000    0.000 reprlib.py:12(decorating_function)
        5    0.000    0.000    0.000    0.000 warnings.py:170(simplefilter)
        3    0.000    0.000    0.000    0.000 selector_events.py:260(_ensure_fd_no_transport)
        1    0.000    0.000    0.000    0.000 client_proto.py:28(__init__)
        3    0.000    0.000    0.000    0.000 __init__.py:931(__init__)
        1    0.000    0.000    0.000    0.000 enum.py:842(_create_)
        1    0.000    0.000    0.000    0.000 manager.py:113(__init__)
        1    0.000    0.000    0.000    0.000 {method 'getsockopt' of '_socket.socket' objects}
       10    0.000    0.000    0.000    0.000 _make.py:2621(<genexpr>)
        2    0.000    0.000    0.000    0.000 base_events.py:932(getaddrinfo)
        1    0.000    0.000    0.000    0.000 request.py:1685(URLopener)
        3    0.000    0.000    0.000    0.000 socket.py:501(close)
       12    0.000    0.000    0.000    0.000 __init__.py:167(<lambda>)
        4    0.000    0.000    0.002    0.000 connector.py:1179(_create_connection)
        1    0.000    0.000    0.000    0.000 mimetypes.py:64(MimeTypes)
        1    0.000    0.000    0.000    0.000 selector_events.py:53(BaseSelectorEventLoop)
        1    0.000    0.000    0.000    0.000 weakref.py:105(remove)
        2    0.000    0.000    0.000    0.000 __init__.py:65(_open)
        1    0.000    0.000    0.001    0.001 idna.py:1(<module>)
        1    0.000    0.000    0.000    0.000 _staggered.py:1(<module>)
        1    0.000    0.000    0.000    0.000 parser.py:62(Reader)
        3    0.000    0.000    0.004    0.001 ssl.py:949(do_handshake)
        2    0.000    0.000    0.000    0.000 streams.py:536(_read_nowait)
        1    0.000    0.000    0.000    0.000 abc.py:174(AbstractCookieJar)
        2    0.000    0.000    0.000    0.000 timeouts.py:145(timeout)
       64    0.000    0.000    0.000    0.000 {method 'isalnum' of 'str' objects}
       24    0.000    0.000    0.000    0.000 __init__.py:470(format)
        4    0.000    0.000    0.001    0.000 _make.py:1821(_add_repr)
        1    0.000    0.000    0.000    0.000 random.py:458(choices)
        1    0.000    0.000    0.001    0.001 http_exceptions.py:1(<module>)
       12    0.000    0.000    0.000    0.000 {built-in method posix._path_splitroot_ex}
        7    0.000    0.000    0.000    0.000 typing.py:1776(__repr__)
       17    0.000    0.000    0.000    0.000 {built-in method _asyncio.current_task}
       15    0.000    0.000    0.000    0.000 _local.py:505(__new__)
        3    0.000    0.000    0.000    0.000 contextlib.py:485(__init__)
        7    0.000    0.000    0.000    0.000 {method 'join' of 'bytes' objects}
        2    0.000    0.000    0.000    0.000 streams.py:126(__init__)
        1    0.000    0.000    0.000    0.000 abc.py:64(AbstractMatchInfo)
        1    0.000    0.000    0.000    0.000 ipaddress.py:1579(_IPv4Constants)
        1    0.000    0.000    0.000    0.000 streams.py:135(__init__)
       24    0.000    0.000    0.000    0.000 __init__.py:391(getMessage)
        1    0.000    0.000    0.000    0.000 {built-in method posix.sched_getaffinity}
        3    0.000    0.000    0.002    0.001 dataclasses.py:1294(wrap)
       17    0.000    0.000    0.000    0.000 {method '__enter__' of '_thread.lock' objects}
        2    0.000    0.000    0.000    0.000 unix_events.py:1493(set_event_loop)
        1    0.000    0.000    0.000    0.000 ssl.py:879(getpeercert)
       80    0.000    0.000    0.000    0.000 <frozen _collections_abc>:439(__subclasshook__)
        1    0.000    0.000    0.005    0.005 converters.py:1(<module>)
       16    0.000    0.000    0.000    0.000 enum.py:1449(_iter_member_by_def_)
        1    0.000    0.000    0.000    0.000 _abc.py:662(_glob_selector)
        4    0.000    0.000    0.000    0.000 _base.py:337(_invoke_callbacks)
       10    0.000    0.000    0.000    0.000 enum.py:1220(_add_alias_)
       43    0.000    0.000    0.000    0.000 enum.py:91(_is_single_bit)
        8    0.000    0.000    0.000    0.000 {built-in method posix.putenv}
        4    0.000    0.000    0.000    0.000 futures.py:378(_set_state)
       24    0.000    0.000    0.000    0.000 __init__.py:683(formatMessage)
        1    0.000    0.000    0.000    0.000 client.py:1458(_BaseRequestContextManager)
        1    0.000    0.000    0.000    0.000 video_generator.py:471(cleanup_temp_files)
        1    0.000    0.000    0.000    0.000 datetime.py:1(<module>)
        1    0.000    0.000    0.000    0.000 thread.py:127(__init__)
        1    0.000    0.000    0.000    0.000 tracing.py:46(TraceConfig)
        1    0.000    0.000    0.001    0.001 base_events.py:625(_do_shutdown)
       16    0.000    0.000    0.000    0.000 selectors.py:276(get_map)
        2    0.000    0.000    0.000    0.000 selectors.py:568(_can_use)
       12    0.000    0.000    0.000    0.000 futures.py:298(_get_loop)
        1    0.000    0.000    0.000    0.000 base_subprocess.py:1(<module>)
        1    0.000    0.000    0.000    0.000 events.py:108(TimerHandle)
       47    0.000    0.000    0.000    0.000 {method 'issuperset' of 'frozenset' objects}
        4    0.000    0.000    0.000    0.000 argparse.py:1109(__init__)
        1    0.000    0.000    0.000    0.000 timeouts.py:26(Timeout)
        4    0.000    0.000    0.001    0.000 manager.py:184(get_audio_preview)
        8    0.000    0.000    0.000    0.000 {built-in method _abc._abc_register}
        3    0.000    0.000    0.000    0.000 {built-in method _imp.exec_builtin}
        2    0.000    0.000    0.000    0.000 selectors.py:213(__init__)
        1    0.000    0.000    0.004    0.004 _policybase.py:1(<module>)
        8    0.000    0.000    0.000    0.000 parser.py:116(parse_unquoted_value)
       10    0.000    0.000    0.000    0.000 argparse.py:1695(_add_action)
       32    0.000    0.000    0.000    0.000 typing.py:1742(<genexpr>)
       18    0.000    0.000    0.000    0.000 parser.py:105(parse_key)
        1    0.000    0.000    0.000    0.000 tasks.py:71(Task)
        2    0.000    0.000    0.001    0.000 base_events.py:1058(create_connection)
        4    0.000    0.000    0.000    0.000 _base.py:497(set_running_or_notify_cancel)
        1    0.000    0.000    0.000    0.000 queues.py:33(Queue)
        1    0.000    0.000    0.000    0.000 ssl.py:871(write)
        4    0.000    0.000    0.000    0.000 threading.py:519(release)
       37    0.000    0.000    0.000    0.000 parser.py:68(has_next)
        1    0.000    0.000    0.000    0.000 selector_events.py:928(__init__)
       10    0.000    0.000    0.000    0.000 argparse.py:2613(_get_formatter)
        3    0.000    0.000    0.000    0.000 base_events.py:662(_run_forever_cleanup)
       11    0.000    0.000    0.000    0.000 typing.py:310(_check_generic_specialization)
       31    0.000    0.000    0.000    0.000 {method 'insert' of 'list' objects}
        1    0.000    0.000    0.001    0.001 _quoting.py:1(<module>)
        4    0.000    0.000    0.000    0.000 _compiler.py:394(_bytes_to_codes)
       32    0.000    0.000    0.000    0.000 typing.py:1221(<genexpr>)
        1    0.000    0.000    0.000    0.000 freesound.py:34(_get_session)
        2    0.000    0.000    0.001    0.000 thread.py:220(shutdown)
        1    0.000    0.000    0.000    0.000 payload.py:952(AsyncIterablePayload)
        1    0.000    0.000    0.000    0.000 unix_events.py:69(close)
        1    0.000    0.000    0.000    0.000 ssl.py:792(SSLObject)
        1    0.000    0.000    0.000    0.000 struct.py:1(<module>)
       14    0.000    0.000    0.000    0.000 <frozen genericpath>:16(exists)
       37    0.000    0.000    0.000    0.000 _make.py:1207(_determine_attrs_eq_order)
        5    0.000    0.000    0.000    0.000 threading.py:592(__init__)
        1    0.000    0.000    0.000    0.000 argparse.py:1845(add_subparsers)
        1    0.000    0.000    0.000    0.000 _next_gen.py:1(<module>)
        1    0.000    0.000    0.000    0.000 queue.py:40(Queue)
        1    0.000    0.000    0.000    0.000 connector.py:998(_close)
        1    0.000    0.000    0.000    0.000 socket.py:653(socketpair)
        8    0.000    0.000    0.000    0.000 sslproto.py:852(_get_write_buffer_size)
        1    0.000    0.000    0.000    0.000 helpers.py:591(timer)
        1    0.000    0.000    0.000    0.000 error.py:35(HTTPError)
        1    0.000    0.000    0.000    0.000 cookiejar.py:88(__init__)
        1    0.000    0.000    0.000    0.000 sslproto.py:612(_start_shutdown)
        1    0.000    0.000    0.000    0.000 {method 'write' of '_ssl._SSLSocket' objects}
        1    0.000    0.000    0.000    0.000 __init__.py:1463(Logger)
        1    0.000    0.000    0.000    0.000 connector.py:220(release)
        2    0.000    0.000    0.000    0.000 subprocess.py:222(<lambda>)
        1    0.000    0.000    0.000    0.000 manager.py:26(__init__)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:702(get_encoding)
        6    0.000    0.000    0.000    0.000 {built-in method _heapq.heappop}
        1    0.000    0.000    0.000    0.000 sslproto.py:453(eof_received)
        1    0.000    0.000    0.000    0.000 abc.py:209(AbstractStreamWriter)
        1    0.000    0.000    0.000    0.000 _helpers.py:1(<module>)
      3/1    0.000    0.000    0.000    0.000 typing.py:1483(_determine_new_args)
        1    0.000    0.000    0.000    0.000 {method 'getsockname' of '_socket.socket' objects}
        1    0.000    0.000    0.000    0.000 base_tasks.py:1(<module>)
        2    0.000    0.000    0.000    0.000 {method 'deleter' of 'property' objects}
       47    0.000    0.000    0.000    0.000 <frozen abc>:7(abstractmethod)
        4    0.000    0.000    0.000    0.000 argparse.py:2492(_get_values)
       14    0.000    0.000    0.000    0.000 {method '__typing_prepare_subst__' of 'typing.TypeVar' objects}
        1    0.000    0.000    0.000    0.000 _abc.py:46(MutableMultiMapping)
        5    0.000    0.000    0.000    0.000 warnings.py:188(_add_filter)
        3    0.000    0.000    0.000    0.000 dataclasses.py:407(_fields_in_init_order)
        1    0.000    0.000    0.000    0.000 request.py:287(Request)
        2    0.000    0.000    0.000    0.000 unix_events.py:1507(get_child_watcher)
        1    0.000    0.000    0.000    0.000 _parse.py:25(split_url)
       28    0.000    0.000    0.000    0.000 dataclasses.py:691(_is_classvar)
        1    0.000    0.000    0.000    0.000 sslproto.py:661(_on_shutdown_complete)
        5    0.000    0.000    0.000    0.000 _local.py:717(mkdir)
        1    0.000    0.000    0.000    0.000 video_generator.py:1(<module>)
        4    0.000    0.000    0.000    0.000 {method 'tolist' of 'memoryview' objects}
        4    0.000    0.000    0.000    0.000 _base.py:328(__init__)
        1    0.000    0.000    0.000    0.000 http_parser.py:755(HttpPayloadParser)
        2    0.000    0.000    0.000    0.000 sslproto.py:103(is_closing)
        1    0.000    0.000    0.000    0.000 queues.py:1(<module>)
        1    0.000    0.000    0.003    0.003 staggered.py:1(<module>)
        2    0.000    0.000    0.000    0.000 utils.py:41(method)
        4    0.000    0.000    0.000    0.000 _base.py:393(done)
      104    0.000    0.000    0.000    0.000 enum.py:248(__init__)
       19    0.000    0.000    0.000    0.000 enum.py:807(__members__)
      112    0.000    0.000    0.000    0.000 ipaddress.py:574(__int__)
        8    0.000    0.000    0.000    0.000 _local.py:148(__truediv__)
        2    0.000    0.000    0.000    0.000 manager.py:133(search_all_sources)
        1    0.000    0.000    0.000    0.000 helpers.py:710(HeadersMixin)
        4    0.000    0.000    0.000    0.000 _base.py:408(add_done_callback)
        4    0.000    0.000    0.000    0.000 contextlib.py:108(__init__)
        1    0.000    0.000    0.000    0.000 client.py:252(HTTPResponse)
        1    0.000    0.000    0.000    0.000 selector_events.py:118(_make_self_pipe)
        8    0.000    0.000    0.000    0.000 <frozen os>:720(__setitem__)
        1    0.000    0.000    0.000    0.000 video_generator.py:76(__init__)
       40    0.000    0.000    0.000    0.000 {method 'partition' of 'str' objects}
        1    0.000    0.000    0.001    0.001 string.py:1(<module>)
        1    0.000    0.000    0.000    0.000 intranges.py:1(<module>)
        2    0.000    0.000    0.000    0.000 {method 'setsockopt' of '_socket.socket' objects}
        8    0.000    0.000    0.000    0.000 argparse.py:2543(_get_value)
        3    0.000    0.000    0.000    0.000 subprocess.py:1275(wait)
        2    0.000    0.000    0.000    0.000 weakref.py:289(update)
       37    0.000    0.000    0.000    0.000 _make.py:2047(_determine_setters)
        2    0.000    0.000    0.000    0.000 log.py:1(<module>)
        4    0.000    0.000    0.000    0.000 ssl.py:399(__new__)
        1    0.000    0.000    0.000    0.000 calendar.py:96(_localized_month)
        5    0.000    0.000    0.000    0.000 threading.py:1267(_make_invoke_excepthook)
        1    0.000    0.000    0.001    0.001 _version_info.py:1(<module>)
        2    0.000    0.000    0.000    0.000 unix_events.py:629(_call_connection_lost)
        1    0.000    0.000    0.000    0.000 random.py:103(Random)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:682(read)
        5    0.000    0.000    0.000    0.000 futures.py:310(_set_result_unless_cancelled)
        4    0.000    0.000    0.000    0.000 <frozen posixpath>:376(abspath)
        4    0.000    0.000    0.000    0.000 socket.py:517(family)
       19    0.000    0.000    0.000    0.000 _parser.py:303(checkgroupname)
        9    0.000    0.000    0.000    0.000 {method 'sub' of 're.Pattern' objects}
       21    0.000    0.000    0.000    0.000 __init__.py:205(_checkLevel)
        3    0.000    0.000    0.000    0.000 weakref.py:367(__init__)
        4    0.000    0.000    0.000    0.000 unix_events.py:529(_add_reader)
        2    0.000    0.000    0.000    0.000 manager.py:248(close)
        4    0.000    0.000    0.000    0.000 {method 'setblocking' of '_socket.socket' objects}
        9    0.000    0.000    0.000    0.000 threading.py:122(RLock)
        1    0.000    0.000    0.000    0.000 subprocess.py:476(CompletedProcess)
       48    0.000    0.000    0.000    0.000 typing.py:1803(<genexpr>)
        2    0.000    0.000    0.000    0.000 _weakrefset.py:63(__iter__)
        2    0.000    0.000    0.000    0.000 warnings.py:653(_deprecated)
        4    0.000    0.000    0.000    0.000 unix_events.py:189(_make_read_pipe_transport)
        1    0.000    0.000    0.000    0.000 cookiejar.py:491(DummyCookieJar)
        4    0.000    0.000    0.000    0.000 signal.py:36(_enum_to_int)
        1    0.000    0.000    0.000    0.000 variables.py:1(<module>)
        2    0.000    0.000    0.000    0.000 events.py:830(set_event_loop)
        1    0.000    0.000    0.000    0.000 threading.py:858(Thread)
        1    0.000    0.000    0.000    0.000 selector_events.py:669(_sock_write_done)
        3    0.000    0.000    0.000    0.000 _abc.py:442(exists)
        1    0.000    0.000    0.000    0.000 lofi_manager.py:322(parse_style)
        1    0.000    0.000    0.001    0.001 reader.py:1(<module>)
       12    0.000    0.000    0.000    0.000 <frozen _collections_abc>:971(clear)
        3    0.000    0.000    0.000    0.000 random.py:119(__init__)
        1    0.000    0.000    0.000    0.000 _funcs.py:1(<module>)
        1    0.000    0.000    0.000    0.000 _url.py:565(_cache_netloc)
        1    0.000    0.000    0.000    0.000 tasks.py:769(cancel)
        1    0.000    0.000    0.000    0.000 random.py:245(_randbelow_with_getrandbits)
       57    0.000    0.000    0.000    0.000 enum.py:799(<genexpr>)
        1    0.000    0.000    0.000    0.000 sslproto.py:536(_start_handshake)
        1    0.000    0.000    0.000    0.000 unix_events.py:1485(_init_watcher)
       16    0.000    0.000    0.000    0.000 main.py:251(<genexpr>)
        1    0.000    0.000    0.000    0.000 ipaddress.py:1933(IPv6Address)
        1    0.000    0.000    0.000    0.000 events.py:789(_init_event_loop_policy)
        1    0.000    0.000    0.000    0.000 helpers.py:122(BasicAuth)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:772(__aexit__)
        4    0.000    0.000    0.001    0.000 lofi_manager.py:260(download_track_audio)
        3    0.000    0.000    0.000    0.000 _make.py:1628(_add_hash)
        1    0.000    0.000    0.000    0.000 tempfile.py:301(_gettempdir)
       27    0.000    0.000    0.000    0.000 argparse.py:1413(_registry_get)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:312(__init__)
        2    0.000    0.000    0.000    0.000 sslproto.py:364(_wakeup_waiter)
        2    0.000    0.000    0.000    0.000 hashlib.py:82(__get_builtin_constructor)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:65(ClientResponseError)
        3    0.000    0.000    0.000    0.000 base_subprocess.py:255(_try_finish)
        1    0.000    0.000    0.001    0.001 string.py:69(__init_subclass__)
        1    0.000    0.000    0.000    0.000 client_proto.py:197(set_exception)
        2    0.000    0.000    0.000    0.000 streams.py:667(read)
        2    0.000    0.000    0.000    0.000 charset.py:206(__init__)
        2    0.000    0.000    0.000    0.000 helpers.py:696(ceil_timeout)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1049(update_headers)
        1    0.000    0.000    0.000    0.000 errors.py:1(<module>)
        1    0.000    0.000    0.000    0.000 bz2.py:26(BZ2File)
       44    0.000    0.000    0.000    0.000 parser.py:80(peek)
        1    0.000    0.000    0.000    0.000 _abc.py:101(PurePathBase)
        6    0.000    0.000    0.000    0.000 base_events.py:1957(_timer_handle_cancelled)
        1    0.000    0.000    0.000    0.000 freesound.py:20(FreesoundClient)
        1    0.000    0.000    0.008    0.008 ssl.py:528(load_default_certs)
        3    0.000    0.000    0.000    0.000 socket.py:523(type)
        3    0.000    0.000    0.000    0.000 sslproto.py:881(_control_ssl_reading)
        1    0.000    0.000    0.000    0.000 helpers.py:514(weakref_handle)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:386(ClientConnectorCertificateError)
        5    0.000    0.000    0.000    0.000 sslproto.py:496(_set_state)
        3    0.000    0.000    0.000    0.000 argparse.py:1161(__init__)
       14    0.000    0.000    0.000    0.000 threading.py:318(_is_owned)
        2    0.000    0.000    0.000    0.000 base_subprocess.py:192(<lambda>)
        1    0.000    0.000    0.000    0.000 streams.py:625(__init__)
        1    0.000    0.000    0.000    0.000 decoder.py:340(decode)
        3    0.000    0.000    0.000    0.000 ssl.py:555(options)
        1    0.000    0.000    0.000    0.000 __init__.py:1194(__init__)
        1    0.000    0.000    0.000    0.000 ipaddress.py:563(_BaseAddress)
        5    0.000    0.000    0.000    0.000 warnings.py:462(__init__)
        1    0.000    0.000    0.000    0.000 heapq.py:1(<module>)
       26    0.000    0.000    0.000    0.000 ipaddress.py:431(_ip_int_from_prefix)
        1    0.000    0.000    0.000    0.000 selector_events.py:110(_close_self_pipe)
        1    0.000    0.000    0.000    0.000 _url.py:100(_InternalURLCache)
       28    0.000    0.000    0.000    0.000 dataclasses.py:591(_init_param)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:261(__init__)
        2    0.000    0.000    0.000    0.000 base_events.py:781(call_later)
       12    0.000    0.000    0.000    0.000 weakref.py:495(popitem)
        7    0.000    0.000    0.000    0.000 unix_events.py:83(_process_self_data)
        2    0.000    0.000    0.001    0.000 runners.py:131(_lazy_init)
        1    0.000    0.000    0.000    0.000 platform.py:926(uname_result)
        1    0.000    0.000    0.000    0.000 __init__.py:14(FrozenList)
        2    0.000    0.000    0.000    0.000 base.py:65(__aexit__)
        1    0.000    0.000    0.000    0.000 constants.py:38(_SendfileMode)
        4    0.000    0.000    0.000    0.000 streams.py:416(__init__)
        6    0.000    0.000    0.000    0.000 contextlib.py:552(_push_exit_callback)
        1    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_sha1}
        2    0.000    0.000    0.000    0.000 ssl.py:677(verify_mode)
        2    0.000    0.000    0.000    0.000 lofi_cli.py:254(cleanup)
        2    0.000    0.000    0.000    0.000 unix_events.py:1421(add_child_handler)
      3/0    0.000    0.000    0.000          threading.py:1000(_bootstrap)
        1    0.000    0.000    0.000    0.000 selector_events.py:1183(_call_connection_lost)
        1    0.000    0.000    0.000    0.000 base.py:53(Track)
        4    0.000    0.000    0.000    0.000 functools.py:66(wraps)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1074(update_auto_headers)
       12    0.000    0.000    0.000    0.000 threading.py:1096(name)
       12    0.000    0.000    0.000    0.000 __init__.py:129(getLevelName)
       12    0.000    0.000    0.000    0.000 argparse.py:1624(_get_handler)
       13    0.000    0.000    0.000    0.000 {built-in method sys._getframe}
        6    0.000    0.000    0.000    0.000 {built-in method sys.set_asyncgen_hooks}
        4    0.000    0.000    0.000    0.000 threading.py:1020(_set_ident)
        1    0.000    0.000    0.000    0.000 header.py:536(_Accumulator)
       20    0.000    0.000    0.000    0.000 enum.py:1737(_simple_enum)
       30    0.000    0.000    0.000    0.000 {method '__exit__' of '_thread.lock' objects}
        1    0.000    0.000    0.001    0.001 __init__.py:41(__getattr__)
        1    0.000    0.000    0.000    0.000 {built-in method time.gmtime}
        1    0.000    0.000    0.000    0.000 _policybase.py:112(Policy)
       85    0.000    0.000    0.000    0.000 _compat_pickle.py:166(<genexpr>)
        2    0.000    0.000    0.002    0.001 main.py:67(dict)
       19    0.000    0.000    0.001    0.000 main.py:24(with_warn_for_invalid_lines)
        1    0.000    0.000    0.000    0.000 random.py:336(randint)
        1    0.000    0.000    0.000    0.000 selectors.py:330(_PollLikeSelector)
        2    0.000    0.000    0.000    0.000 impl.py:157(_connect_sock)
        1    0.000    0.000    0.000    0.000 tcp_helpers.py:1(<module>)
        1    0.000    0.000    0.000    0.000 client.py:450(__del__)
        1    0.000    0.000    0.000    0.000 coroutines.py:1(<module>)
        1    0.000    0.000    0.000    0.000 _abc.py:28(MultiMapping)
        1    0.000    0.000    0.000    0.000 lzma.py:38(LZMAFile)
        2    0.000    0.000    0.000    0.000 helpers.py:664(__exit__)
        1    0.000    0.000    0.000    0.000 ipaddress.py:383(_IPAddressBase)
        5    0.000    0.000    0.000    0.000 _abc.py:460(is_dir)
        8    0.000    0.000    0.000    0.000 _local.py:510(stat)
        1    0.000    0.000    0.000    0.000 {method 'replace' of 'datetime.datetime' objects}
        1    0.000    0.000    0.000    0.000 argparse.py:155(HelpFormatter)
        1    0.000    0.000    0.000    0.000 text.py:1(<module>)
        1    0.000    0.000    0.000    0.000 thread.py:122(ThreadPoolExecutor)
        1    0.000    0.000    0.000    0.000 inspect.py:3094(from_callable)
        1    0.000    0.000    0.000    0.000 base_events.py:192(_set_nodelay)
        1    0.000    0.000    0.000    0.000 functools.py:844(dispatch)
       12    0.000    0.000    0.001    0.000 gettext.py:627(gettext)
        3    0.000    0.000    0.000    0.000 client.py:1324(closed)
        1    0.000    0.000    0.000    0.000 streams.py:390(read)
       34    0.000    0.000    0.000    0.000 typing.py:1807(__eq__)
        1    0.000    0.000    0.000    0.000 ipaddress.py:1621(_BaseV6)
        1    0.000    0.000    0.000    0.000 _query.py:79(get_str_query)
        1    0.000    0.000    0.000    0.000 {method 'read' of '_io.TextIOWrapper' objects}
        5    0.000    0.000    0.000    0.000 typing.py:395(_flatten_literal_params)
        1    0.000    0.000    0.000    0.000 glob.py:527(_StringGlobber)
        1    0.000    0.000    0.000    0.000 calendar.py:115(_localized_day)
        2    0.000    0.000    0.000    0.000 client_reqrep.py:936(is_ssl)
        8    0.000    0.000    0.000    0.000 {built-in method builtins.all}
        2    0.000    0.000    0.000    0.000 __init__.py:601(__init__)
        2    0.000    0.000    0.000    0.000 selectors.py:272(close)
        4    0.000    0.000    0.000    0.000 {built-in method posix.set_blocking}
        1    0.000    0.000    0.000    0.000 format_helpers.py:1(<module>)
        1    0.000    0.000    0.000    0.000 traceback.py:276(FrameSummary)
        2    0.000    0.000    0.000    0.000 signal.py:62(getsignal)
        9    0.000    0.000    0.000    0.000 coroutines.py:32(iscoroutine)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:177(ClientConnectorError)
       14    0.000    0.000    0.000    0.000 <frozen abc>:117(__instancecheck__)
        1    0.000    0.000    0.000    0.000 validators.py:379(_DeepMapping)
        2    0.000    0.000    0.000    0.000 lofi_manager.py:157(get_track_for_video)
        1    0.000    0.000    0.000    0.000 streams.py:655(feed_data)
        1    0.000    0.000    0.000    0.000 _make.py:621(_ClassBuilder)
        1    0.000    0.000    0.000    0.000 glob.py:370(selector)
        1    0.000    0.000    0.000    0.000 fnmatch.py:1(<module>)
        5    0.000    0.000    0.000    0.000 {method 'get_nowait' of '_queue.SimpleQueue' objects}
        2    0.000    0.000    0.000    0.000 events.py:721(set_event_loop)
        1    0.000    0.000    0.000    0.000 __init__.py:1111(StreamHandler)
        4    0.000    0.000    0.000    0.000 _base.py:428(result)
        1    0.000    0.000    0.000    0.000 streams.py:552(EmptyStreamReader)
        1    0.000    0.000    0.000    0.000 tracing.py:335(Trace)
       22    0.000    0.000    0.000    0.000 lofi_manager.py:116(<genexpr>)
        1    0.000    0.000    0.000    0.000 helpers.py:581(start)
       11    0.000    0.000    0.000    0.000 {method 'add_done_callback' of '_asyncio.Task' objects}
        1    0.000    0.000    0.000    0.000 client.py:1498(_SessionRequestContextManager)
        1    0.000    0.000    0.000    0.000 selector_events.py:308(_add_writer)
        1    0.000    0.000    0.000    0.000 __init__.py:1447(_clear_cache)
        1    0.000    0.000    0.000    0.000 connector.py:477(close)
        1    0.000    0.000    0.000    0.000 request.py:1197(HTTPDigestAuthHandler)
        1    0.000    0.000    0.000    0.000 parse.py:152(_NetlocResultMixinBase)
        1    0.000    0.000    0.000    0.000 threading.py:1352(__init__)
        1    0.000    0.000    0.000    0.000 base_subprocess.py:13(BaseSubprocessTransport)
        1    0.000    0.000    0.000    0.000 subprocess.py:1978(_handle_exitstatus)
       77    0.000    0.000    0.000    0.000 signal.py:16(<lambda>)
        1    0.000    0.000    0.000    0.000 _base.py:325(Future)
       78    0.000    0.000    0.000    0.000 signal.py:21(<lambda>)
        9    0.000    0.000    0.000    0.000 argparse.py:2324(_parse_optional)
       19    0.000    0.000    0.000    0.000 __init__.py:820(__init__)
        1    0.000    0.000    0.000    0.000 weakref.py:164(__setitem__)
       27    0.000    0.000    0.000    0.000 <frozen _collections_abc>:345(__subclasshook__)
        1    0.000    0.000    0.000    0.000 streams.py:210(feed_eof)
        1    0.000    0.000    0.000    0.000 unix_events.py:230(_child_watcher_callback)
        1    0.000    0.000    0.000    0.000 __future__.py:1(<module>)
       18    0.000    0.000    0.000    0.000 parser.py:71(set_mark)
       15    0.000    0.000    0.000    0.000 dataclasses.py:865(_set_new_attribute)
        8    0.000    0.000    0.000    0.000 {method 'finditer' of 're.Pattern' objects}
        1    0.000    0.000    0.000    0.000 payload.py:826(BytesIOPayload)
        1    0.000    0.000    0.000    0.000 selector_events.py:923(_SelectorSocketTransport)
       14    0.000    0.000    0.000    0.000 {built-in method _asyncio.get_running_loop}
        6    0.000    0.000    0.000    0.000 base_events.py:635(_check_running)
        1    0.000    0.000    0.000    0.000 message.py:981(MIMEPart)
        1    0.000    0.000    0.000    0.000 manager.py:34(_get_cache_key)
        1    0.000    0.000    0.000    0.000 base_protocol.py:9(BaseProtocol)
        5    0.000    0.000    0.000    0.000 client_proto.py:257(_drop_timeout)
        7    0.000    0.000    0.000    0.000 transports.py:14(__init__)
       34    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1307(__init__)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:301(InvalidURL)
        2    0.000    0.000    0.000    0.000 tasks.py:763(__init__)
        8    0.000    0.000    0.000    0.000 <frozen _collections_abc>:823(keys)
        2    0.000    0.000    0.000    0.000 base.py:90(__post_init__)
        9    0.000    0.000    0.000    0.000 __init__.py:185(__new__)
        2    0.000    0.000    0.000    0.000 base.py:57(__await__)
        1    0.000    0.000    0.000    0.000 threading.py:461(__init__)
        1    0.000    0.000    0.000    0.000 bisect.py:1(<module>)
        6    0.000    0.000    0.000    0.000 contextlib.py:479(_create_cb_wrapper)
        1    0.000    0.000    0.000    0.000 selectors.py:84(BaseSelector)
        8    0.000    0.000    0.000    0.000 __init__.py:183(sub)
       10    0.000    0.000    0.000    0.000 argparse.py:1620(_pop_action_class)
        1    0.000    0.000    0.000    0.000 http_writer.py:89(_write)
        8    0.000    0.000    0.000    0.000 parser.py:121(parse_value)
       42    0.000    0.000    0.000    0.000 _compat_pickle.py:164(<genexpr>)
        2    0.000    0.000    0.000    0.000 subprocess.py:74(pipe_connection_lost)
       28    0.000    0.000    0.000    0.000 dataclasses.py:699(_is_initvar)
        1    0.000    0.000    0.000    0.000 models.py:50(WSMessage)
        1    0.000    0.000    0.000    0.000 _compat.py:62(get_return_type)
        1    0.000    0.000    0.000    0.000 types.py:276(coroutine)
        2    0.000    0.000    0.002    0.001 ssl.py:430(__new__)
        1    0.000    0.000    0.000    0.000 validators.py:594(_NotValidator)
        1    0.000    0.000    0.000    0.000 _make.py:61(_Nothing)
        1    0.000    0.000    0.000    0.000 weakref.py:369(remove)
       14    0.000    0.000    0.000    0.000 {method 'done' of '_asyncio.Task' objects}
        1    0.000    0.000    0.000    0.000 connector.py:757(_release_acquired)
        1    0.000    0.000    0.002    0.002 main.py:327(load_dotenv)
        1    0.000    0.000    0.000    0.000 base.py:128(MusicSource)
        1    0.000    0.000    0.000    0.000 base_subprocess.py:230(_process_exited)
        1    0.000    0.000    0.000    0.000 unix_events.py:48(waitstatus_to_exitcode)
        1    0.000    0.000    0.000    0.000 streams.py:309(StreamWriter)
        1    0.000    0.000    0.000    0.000 unix_events.py:495(_UnixReadPipeTransport)
        4    0.000    0.000    0.000    0.000 contextlib.py:145(__exit__)
        1    0.000    0.000    0.000    0.000 __init__.py:1500(debug)
        1    0.000    0.000    0.000    0.000 _compat.py:49(get_first_param_type)
        1    0.000    0.000    0.000    0.000 socket.py:215(socket)
        2    0.000    0.000    0.000    0.000 sslproto.py:374(_get_app_transport)
        3    0.000    0.000    0.000    0.000 __init__.py:958(createLock)
       26    0.000    0.000    0.000    0.000 ipaddress.py:415(_check_int_address)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1398(__iter__)
        1    0.000    0.000    0.000    0.000 argparse.py:1743(ArgumentParser)
       28    0.000    0.000    0.000    0.000 dataclasses.py:288(__init__)
        5    0.000    0.000    0.000    0.000 warnings.py:509(__exit__)
        2    0.000    0.000    0.000    0.000 selector_events.py:1203(close)
       28    0.000    0.000    0.000    0.000 dataclasses.py:519(_field_assign)
        3    0.000    0.000    0.000    0.000 subprocess.py:108(_maybe_close_transport)
        1    0.000    0.000    0.000    0.000 base.py:33(AsyncIndirectBase)
        1    0.000    0.000    0.000    0.000 models.py:26(WSMsgType)
        3    0.000    0.000    0.000    0.000 __init__.py:899(_addHandlerRef)
        1    0.000    0.000    0.000    0.000 locks.py:352(Semaphore)
        5    0.000    0.000    0.000    0.000 argparse.py:915(__init__)
        5    0.000    0.000    0.000    0.000 events.py:129(__lt__)
        6    0.000    0.000    0.000    0.000 utils.py:14(cls_builder)
        1    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_sha3_224}
        3    0.000    0.000    0.000    0.000 timeouts.py:33(__init__)
        1    0.000    0.000    0.000    0.000 types.py:1(<module>)
        1    0.000    0.000    0.000    0.000 unix_events.py:1065(SafeChildWatcher)
        2    0.000    0.000    0.000    0.000 enum.py:1722(global_enum)
        1    0.000    0.000    0.000    0.000 subprocess.py:2021(_try_wait)
        2    0.000    0.000    0.000    0.000 subprocess.py:1133(__del__)
        2    0.000    0.000    0.000    0.000 streams.py:201(on_eof)
        1    0.000    0.000    0.000    0.000 freesound.py:25(__init__)
        1    0.000    0.000   25.058   25.058 runners.py:61(__exit__)
       14    0.000    0.000    0.000    0.000 typing.py:1754(<genexpr>)
        2    0.000    0.000    0.000    0.000 impl.py:15(start_connection)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.helpers.MimeType>:33(__init__)
        1    0.000    0.000    0.000    0.000 ipaddress.py:1280(IPv4Address)
       12    0.000    0.000    0.000    0.000 {method 'popitem' of 'dict' objects}
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1384(_recalculate)
        3    0.000    0.000    0.000    0.000 ssl.py:559(options)
        1    0.000    0.000    0.000    0.000 _make.py:2593(_CountingAttr)
        1    0.000    0.000    0.000    0.000 argparse.py:211(format_help)
        2    0.000    0.000    0.000    0.000 subprocess.py:119(__init__)
        1    0.000    0.000    0.000    0.000 base_events.py:735(close)
        1    0.000    0.000    0.000    0.000 connector.py:808(_DNSCacheTable)
        1    0.000    0.000    0.000    0.000 sslproto.py:702(_do_write)
        2    0.000    0.000    0.000    0.000 lofi_manager.py:313(close)
        6    0.000    0.000    0.000    0.000 {built-in method posix.register_at_fork}
        1    0.000    0.000    0.000    0.000 streams.py:508(_read_nowait_chunk)
        9    0.000    0.000    0.000    0.000 argparse.py:1876(_add_action)
        2    0.000    0.000    0.000    0.000 sslproto.py:382(_is_transport_closing)
        1    0.000    0.000    0.000    0.000 {built-in method time.get_clock_info}
        1    0.000    0.000    0.000    0.000 client_proto.py:185(eof_received)
        1    0.000    0.000    0.000    0.000 inspect.py:3373(signature)
        2    0.000    0.000    0.000    0.000 client_reqrep.py:600(release)
        1    0.000    0.000    0.000    0.000 models.py:10(WSCloseCode)
        2    0.000    0.000    0.000    0.000 {built-in method _signal.signal}
       28    0.000    0.000    0.000    0.000 _make.py:427(<lambda>)
        1    0.000    0.000    0.000    0.000 {method 'timestamp' of 'datetime.datetime' objects}
        1    0.000    0.000    0.000    0.000 base64mime.py:1(<module>)
        8    0.000    0.000    0.000    0.000 <frozen _collections_abc>:815(__contains__)
        1    0.000    0.000    0.000    0.000 tasks.py:958(_inner_done_callback)
        1    0.000    0.000    0.000    0.000 connector.py:1690(UnixConnector)
       17    0.000    0.000    0.000    0.000 ssl.py:972(_sslcopydoc)
        1    0.000    0.000    0.000    0.000 taskgroups.py:12(TaskGroup)
        1    0.000    0.000    0.000    0.000 subprocess.py:108(_del_safe)
        3    0.000    0.000    0.000    0.000 __init__.py:1758(getEffectiveLevel)
        1    0.000    0.000    0.000    0.000 helpers.py:555(TimeoutHandle)
        1    0.000    0.000    0.000    0.000 _make.py:2832(__init__)
        1    0.000    0.000    0.000    0.000 base.py:77(SearchQuery)
        1    0.000    0.000    0.000    0.000 tasks.py:918(shield)
        8    0.000    0.000    0.000    0.000 {method 'clear' of 'dict' objects}
        1    0.000    0.000    0.000    0.000 base.py:183(acquire)
        1    0.000    0.000    0.000    0.000 sslproto.py:93(get_extra_info)
       22    0.000    0.000    0.000    0.000 functools.py:871(_is_valid_dispatch_type)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1380(_get_parent_path)
        1    0.000    0.000    0.000    0.000 lofi_manager.py:37(__init__)
        1    0.000    0.000    0.000    0.000 decoder.py:292(__init__)
        1    0.000    0.000    0.000    0.000 streams.py:77(AsyncStreamReaderMixin)
        7    0.000    0.000    0.000    0.000 typing.py:235(_type_repr)
        6    0.000    0.000    0.000    0.000 base_events.py:2045(_set_coroutine_origin_tracking)
        1    0.000    0.000    0.000    0.000 inspect.py:3042(__init__)
        1    0.000    0.000    0.000    0.000 trsock.py:4(TransportSocket)
        1    0.000    0.000    0.000    0.000 _path.py:1(<module>)
        1    0.000    0.000    0.000    0.000 threading.py:269(Condition)
       10    0.000    0.000    0.000    0.000 {method 'fileno' of '_io.TextIOWrapper' objects}
        1    0.000    0.000    0.000    0.000 calendar.py:60(Month)
        5    0.000    0.000    0.000    0.000 {built-in method _thread.get_native_id}
        1    0.000    0.000    0.000    0.000 events.py:666(BaseDefaultEventLoopPolicy)
        1    0.000    0.000    0.000    0.000 payload.py:430(StringPayload)
        1    0.000    0.000    0.000    0.000 formdata.py:15(FormData)
        1    0.000    0.000    0.000    0.000 inspect.py:764(unwrap)
        1    0.000    0.000    0.000    0.000 calendar.py:194(Calendar)
        1    0.000    0.000    0.000    0.000 __init__.py:8(Signal)
        1    0.000    0.000    0.000    0.000 ipaddress.py:1139(_BaseV4)
       13    0.000    0.000    0.000    0.000 typing.py:1802(_value_and_type_iter)
        1    0.000    0.000    0.000    0.000 _compat.py:43(__init__)
        1    0.000    0.000    0.000    0.000 client_proto.py:74(should_close)
        4    0.000    0.000    0.000    0.000 _base.py:463(exception)
        1    0.000    0.000    0.000    0.000 idna.py:378(getregentry)
        1    0.000    0.000    0.000    0.000 selector_events.py:359(remove_writer)
        1    0.000    0.000    0.000    0.000 __init__.py:778(Filter)
        1    0.000    0.000    0.000    0.000 ssl.py:661(verify_flags)
        1    0.000    0.000    0.000    0.000 parse.py:854(_Quoter)
        1    0.000    0.000    0.000    0.000 setters.py:1(<module>)
        1    0.000    0.000    0.000    0.000 _cmp.py:1(<module>)
        2    0.000    0.000    0.000    0.000 selectors.py:63(__init__)
        1    0.000    0.000    0.000    0.000 base_events.py:1491(_ensure_resolved)
        1    0.000    0.000    0.000    0.000 functools.py:930(wrapper)
        1    0.000    0.000    0.000    0.000 __init__.py:36(open)
        2    0.000    0.000    0.000    0.000 <frozen _collections_abc>:473(__new__)
        4    0.000    0.000    0.000    0.000 base_subprocess.py:280(__init__)
        1    0.000    0.000    0.000    0.000 _url.py:1196(extend_query)
       13    0.000    0.000    0.000    0.000 {built-in method time.time_ns}
        4    0.000    0.000    0.000    0.000 _base.py:383(cancelled)
        1    0.000    0.000    0.000    0.000 http_parser.py:666(HttpResponseParser)
        1    0.000    0.000    0.000    0.000 unix_events.py:1384(ThreadedChildWatcher)
        1    0.000    0.000    0.000    0.000 __init__.py:6(HTTPStatus)
        3    0.000    0.000    0.000    0.000 selectors.py:66(__len__)
        6    0.000    0.000    0.000    0.000 signal.py:51(decorator)
        3    0.000    0.000    0.000    0.000 <frozen os>:1127(__subclasshook__)
        2    0.000    0.000    0.000    0.000 base.py:170(close)
        1    0.000    0.000    0.000    0.000 sslproto.py:678(_write_appdata)
        1    0.000    0.000    0.000    0.000 taskgroups.py:1(<module>)
       12    0.000    0.000    0.000    0.000 {method 'update' of 'set' objects}
        1    0.000    0.000    0.000    0.000 connector.py:817(add)
       10    0.000    0.000    0.000    0.000 argparse.py:1633(_check_conflict)
        1    0.000    0.000    0.000    0.000 ssl.py:813(_create)
        1    0.000    0.000    0.000    0.000 _version_info.py:10(VersionInfo)
        3    0.000    0.000    0.000    0.000 __init__.py:265(_register_at_fork_reinit_lock)
       24    0.000    0.000    0.000    0.000 {method 'reverse' of 'list' objects}
       10    0.000    0.000    0.000    0.000 {method 'get_name' of '_asyncio.Task' objects}
        5    0.000    0.000    0.000    0.000 enum.py:795(__iter__)
       11    0.000    0.000    0.000    0.000 __init__.py:1308(append)
        8    0.000    0.000    0.000    0.000 <frozen abc>:110(register)
        1    0.000    0.000    0.000    0.000 manager.py:23(MusicCache)
        1    0.000    0.000    0.000    0.000 netrc.py:1(<module>)
        2    0.000    0.000    0.000    0.000 platform.py:1071(system)
        1    0.000    0.000    0.000    0.000 sslproto.py:643(_do_flush)
        1    0.000    0.000    0.000    0.000 connector.py:833(next_addrs)
        1    0.000    0.000    0.000    0.000 request.py:1564(CacheFTPHandler)
       10    0.000    0.000    0.000    0.000 _make.py:1145(add_setattr)
        1    0.000    0.000    0.000    0.000 tasks.py:402(create_task)
        4    0.000    0.000    0.000    0.000 {method 'fileno' of '_io.BufferedReader' objects}
        6    0.000    0.000    0.000    0.000 base.py:34(__init__)
        2    0.000    0.000    0.001    0.000 platform.py:1238(python_implementation)
        1    0.000    0.000    0.000    0.000 transports.py:72(WriteTransport)
        2    0.000    0.000    0.000    0.000 streams.py:432(readany)
        1    0.000    0.000    0.000    0.000 __init__.py:922(Handler)
        1    0.000    0.000    0.000    0.000 abc.py:151(AbstractResolver)
        1    0.000    0.000    0.000    0.000 parser.py:63(__init__)
        2    0.000    0.000    0.000    0.000 __init__.py:458(validate)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1003(request_info)
        1    0.000    0.000    0.000    0.000 streams.py:273(feed_data)
        1    0.000    0.000    0.000    0.000 argparse.py:286(format_help)
        1    0.000    0.000    0.000    0.000 helpers.py:773(set_exception)
       11    0.000    0.000    0.000    0.000 functools.py:887(<lambda>)
        1    0.000    0.000    0.000    0.000 request.py:2395(ftpwrapper)
        1    0.000    0.000    0.000    0.000 request.py:2135(FancyURLopener)
        1    0.000    0.000    0.000    0.000 subprocess.py:1249(poll)
        1    0.000    0.000    0.000    0.000 _local.py:30(_PathParents)
        3    0.000    0.000    0.000    0.000 payload.py:87(__call__)
        7    0.000    0.000    0.000    0.000 base.py:8(__init__)
        1    0.000    0.000    0.000    0.000 tempfile.py:864(TemporaryDirectory)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:501(_requires_builtin_wrapper)
        1    0.000    0.000    0.000    0.000 lofi_manager.py:80(_filter_lofi_tracks)
        2    0.000    0.000    0.000    0.000 signal.py:56(signal)
        1    0.000    0.000    0.000    0.000 connector.py:148(__init__)
        1    0.000    0.000    0.000    0.000 traceback.py:428(StackSummary)
        1    0.000    0.000    0.000    0.000 connector.py:1193(_get_ssl_context)
        1    0.000    0.000    0.000    0.000 argparse.py:2016(consume_optional)
        1    0.000    0.000    0.000    0.000 locks.py:476(Barrier)
        5    0.000    0.000    0.000    0.000 _policybase.py:104(<genexpr>)
        1    0.000    0.000    0.000    0.000 lofi_cli.py:33(LoFiCLI)
        1    0.000    0.000    0.000    0.000 cookiejar.py:306(filter_cookies)
        4    0.000    0.000    0.000    0.000 contextlib.py:136(__enter__)
        6    0.000    0.000    0.000    0.000 {method 'add_done_callback' of '_asyncio.Future' objects}
        1    0.000    0.000    0.000    0.000 manager.py:128(configure_freesound)
        7    0.000    0.000    0.000    0.000 <frozen posixpath>:131(splitdrive)
       14    0.000    0.000    0.000    0.000 _make.py:2023(_assign)
        1    0.000    0.000    0.000    0.000 inspect.py:2754(__init__)
        1    0.000    0.000    0.000    0.000 payload.py:461(StringIOPayload)
       13    0.000    0.000    0.000    0.000 {method 'done' of '_asyncio.Future' objects}
       10    0.000    0.000    0.000    0.000 typing.py:1230(<genexpr>)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:281(ServerFingerprintMismatch)
        1    0.000    0.000    0.000    0.000 helpers.py:631(TimerContext)
        3    0.000    0.000    0.000    0.000 <frozen genericpath>:36(isfile)
        2    0.000    0.000    0.000    0.000 freesound.py:83(_parse_license)
        9    0.000    0.000    0.000    0.000 typing.py:3112(_namedtuple_mro_entries)
        1    0.000    0.000    0.000    0.000 multipart.py:212(MultipartResponseWrapper)
       14    0.000    0.000    0.000    0.000 {built-in method sys._getframemodulename}
        1    0.000    0.000    0.000    0.000 ssl.py:465(wrap_bio)
       10    0.000    0.000    0.000    0.000 _make.py:1538(_has_frozen_base_class)
        1    0.000    0.000    0.000    0.000 tempfile.py:156(_candidate_tempdir_list)
        1    0.000    0.000    0.000    0.000 http_writer.py:59(__init__)
        4    0.000    0.000    0.000    0.000 client_reqrep.py:637(_release_connection)
        1    0.000    0.000    0.000    0.000 http_parser.py:94(RawResponseMessage)
       16    0.000    0.000    0.000    0.000 {method 'pop' of 'set' objects}
        1    0.000    0.000    0.000    0.000 {built-in method posix.uname}
        1    0.000    0.000    0.000    0.000 transports.py:272(__init__)
        1    0.000    0.000    0.000    0.000 unix_events.py:1397(__init__)
        1    0.000    0.000    0.000    0.000 threading.py:581(Event)
        1    0.000    0.000    0.000    0.000 quopri.py:1(<module>)
        9    0.000    0.000    0.000    0.000 argparse.py:557(format)
        1    0.000    0.000    0.000    0.000 runners.py:198(_cancel_all_tasks)
        2    0.000    0.000    0.000    0.000 main.py:52(_get_stream)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:943(connection_key)
        5    0.000    0.000    0.000    0.000 functools.py:995(__init__)
       15    0.000    0.000    0.000    0.000 {method 'values' of 'dict' objects}
        1    0.000    0.000    0.000    0.000 threads.py:1(<module>)
        1    0.000    0.000    0.000    0.000 encoders.py:1(<module>)
        1    0.000    0.000    0.000    0.000 sslproto.py:88(__init__)
        1    0.000    0.000    0.000    0.000 tempfile.py:142(rng)
        1    0.000    0.000    0.000    0.000 _parseaddr.py:1(<module>)
        1    0.000    0.000    0.000    0.000 calendar.py:689(timegm)
        3    0.000    0.000    0.000    0.000 {built-in method sys.exception}
        1    0.000    0.000    0.000    0.000 threading.py:843(_newname)
        1    0.000    0.000    0.000    0.000 _parseaddr.py:515(AddressList)
        1    0.000    0.000    0.000    0.000 request.py:805(HTTPPasswordMgr)
        1    0.000    0.000    0.000    0.000 _base.py:59(_Waiter)
        2    0.000    0.000    0.000    0.000 {method '__typing_subst__' of 'typing.TypeVar' objects}
        1    0.000    0.000    0.000    0.000 sslproto.py:211(write)
        1    0.000    0.000    0.000    0.000 base_events.py:207(_SendfileFallbackProtocol)
        1    0.000    0.000    0.000    0.000 argparse.py:1230(__call__)
       10    0.000    0.000    0.000    0.000 base_subprocess.py:143(get_pipe_transport)
       31    0.000    0.000    0.000    0.000 dataclasses.py:411(<genexpr>)
        1    0.000    0.000    0.000    0.000 streams.py:622(DataQueue)
        1    0.000    0.000    0.000    0.000 calendar.py:330(TextCalendar)
        1    0.000    0.000    0.000    0.000 http_parser.py:949(DeflateBuffer)
        5    0.000    0.000    0.000    0.000 threading.py:1024(_set_native_id)
        1    0.000    0.000    0.000    0.000 subprocess.py:132(CalledProcessError)
        2    0.000    0.000    0.000    0.000 _parser.py:894(_parse_flags)
        1    0.000    0.000    0.000    0.000 payload.py:712(TextIOPayload)
        1    0.000    0.000    0.000    0.000 http_parser.py:81(RawRequestMessage)
        1    0.000    0.000    0.000    0.000 tempfile.py:313(gettempdir)
        1    0.000    0.000    0.000    0.000 __init__.py:1338(Manager)
        1    0.000    0.000    0.000    0.000 _local.py:315(anchor)
        3    0.000    0.000    0.000    0.000 subprocess.py:274(_cleanup)
        2    0.000    0.000    0.000    0.000 base_events.py:202(_check_ssl_socket)
        1    0.000    0.000    0.000    0.000 _make.py:802(_patch_original_class)
        1    0.000    0.000    0.000    0.000 <frozen os>:1174(process_cpu_count)
       17    0.000    0.000    0.000    0.000 typing.py:840(<genexpr>)
        5    0.000    0.000    0.000    0.000 reprlib.py:9(recursive_repr)
        4    0.000    0.000    0.000    0.000 <frozen _collections_abc>:292(__subclasshook__)
        1    0.000    0.000    0.000    0.000 client.py:1506(IncompleteRead)
        1    0.000    0.000    0.000    0.000 argparse.py:1671(_ArgumentGroup)
        1    0.000    0.000    0.000    0.000 __future__.py:81(_Feature)
        1    0.000    0.000    0.000    0.000 abc.py:252(AbstractAccessLogger)
       19    0.000    0.000    0.000    0.000 {built-in method builtins.globals}
        1    0.000    0.000    0.000    0.000 utils.py:32(cls_builder)
        1    0.000    0.000    0.000    0.000 ssl.py:953(unwrap)
       16    0.000    0.000    0.000    0.000 base_events.py:758(is_closed)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._DeepIterable>:14(__init__)
        1    0.000    0.000    0.000    0.000 argparse.py:853(BooleanOptionalAction)
      2/1    0.000    0.000    0.001    0.001 argparse.py:1907(parse_known_args)
        3    0.000    0.000    0.000    0.000 {method 'union' of 'set' objects}
        2    0.000    0.000    0.000    0.000 contextlib.py:444(__init__)
        1    0.000    0.000    0.000    0.000 http_writer.py:236(set_eof)
        1    0.000    0.000    0.000    0.000 resolver.py:27(ThreadedResolver)
        2    0.000    0.000    0.000    0.000 weakref.py:427(__setitem__)
        3    0.000    0.000    0.000    0.000 typing.py:1131(_typevar_subst)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:241(ConnectionKey)
        1    0.000    0.000    0.000    0.000 sslproto.py:488(_get_extra_info)
        1    0.000    0.000    0.000    0.000 traceback.py:997(TracebackException)
        1    0.000    0.000    0.000    0.000 compression_utils.py:74(ZLibBackendWrapper)
        1    0.000    0.000    0.000    0.000 subprocess.py:1105(__exit__)
        1    0.000    0.000    0.000    0.000 client_ws.py:37(ClientWSTimeout)
        1    0.000    0.000    0.000    0.000 parse.py:342(DefragResultBytes)
        3    0.000    0.000    0.000    0.000 payload.py:76(register_payload)
       12    0.000    0.000    0.000    0.000 typing.py:3155(<genexpr>)
        1    0.000    0.000    0.000    0.000 {method 'hexdigest' of '_hashlib.HASH' objects}
        2    0.000    0.000    0.000    0.000 {method 'append' of 'bytearray' objects}
        1    0.000    0.000    0.000    0.000 ssl.py:394(_ASN1Object)
        3    0.000    0.000    0.000    0.000 argparse.py:1571(_get_positional_kwargs)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceResponseChunkReceivedParams>:1(<module>)
        5    0.000    0.000    0.000    0.000 typing.py:1127(_is_typevar_like)
        2    0.000    0.000    0.000    0.000 client_proto.py:262(_reschedule_timeout)
        1    0.000    0.000    0.000    0.000 helpers.py:796(AppKey)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:142(Fingerprint)
        1    0.000    0.000    0.000    0.000 error.py:1(<module>)
       25    0.000    0.000    0.000    0.000 {method '__init_subclass__' of 'object' objects}
        1    0.000    0.000    0.000    0.000 _make.py:2802(Converter)
        9    0.000    0.000    0.000    0.000 argparse.py:549(_metavar_formatter)
        2    0.000    0.000    0.000    0.000 _local.py:252(_from_parsed_string)
        1    0.000    0.000    0.001    0.001 unix_events.py:64(__init__)
        1    0.000    0.000    0.000    0.000 ipaddress.py:2300(IPv6Network)
        2    0.000    0.000    0.000    0.000 _local.py:740(unlink)
        9    0.000    0.000    0.000    0.000 {method 'index' of 'tuple' objects}
       18    0.000    0.000    0.000    0.000 typing.py:3004(<genexpr>)
        5    0.000    0.000    0.000    0.000 threading.py:315(_acquire_restore)
        1    0.000    0.000    0.000    0.000 client_proto.py:107(is_connected)
        1    0.000    0.000    0.000    0.000 compression_utils.py:40(ZLibBackendProtocol)
        1    0.000    0.000    0.000    0.000 selectors.py:465(close)
        1    0.000    0.000    0.000    0.000 selector_events.py:953(set_protocol)
        1    0.000    0.000    0.000    0.000 calendar.py:77(Day)
        1    0.000    0.000    0.000    0.000 __init__.py:444(PercentStyle)
        1    0.000    0.000    0.000    0.000 base.py:24(LoFiStyle)
        1    0.000    0.000    0.000    0.000 __init__.py:554(Formatter)
        1    0.000    0.000    0.000    0.000 client_middleware_digest_auth.py:150(DigestAuthMiddleware)
       28    0.000    0.000    0.000    0.000 dataclasses.py:705(_is_kw_only)
        1    0.000    0.000    0.000    0.000 argparse.py:1278(FileType)
        1    0.000    0.000    0.000    0.000 helpers.py:757(set_result)
        1    0.000    0.000    0.000    0.000 ssl.py:441(_encode_hostname)
        1    0.000    0.000    0.000    0.000 sslproto.py:23(SSLProtocolState)
       31    0.000    0.000    0.000    0.000 dataclasses.py:1111(<genexpr>)
        1    0.000    0.000    0.000    0.000 selector_events.py:917(_add_reader)
        5    0.000    0.000    0.000    0.000 validators.py:314(is_callable)
        1    0.000    0.000    0.000    0.000 {method 'replace' of 'code' objects}
        1    0.000    0.000    0.000    0.000 connector.py:1745(NamedPipeConnector)
        1    0.000    0.000    0.000    0.000 ssl.py:415(Purpose)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:253(_is_expected_content_type)
        1    0.000    0.000    0.001    0.001 __init__.py:477(StrFormatStyle)
        1    0.000    0.000    0.000    0.000 helpers.py:615(TimerNoop)
        1    0.000    0.000    0.000    0.000 queue.py:300(LifoQueue)
        1    0.000    0.000    0.000    0.000 http_parser.py:109(ParseState)
        1    0.000    0.000    0.000    0.000 connector.py:232(_TransportPlaceholder)
        1    0.000    0.000    0.000    0.000 multipart.py:1085(MultipartPayloadWriter)
        1    0.000    0.000    0.000    0.000 _weakrefset.py:27(__exit__)
        2    0.000    0.000    0.000    0.000 _weakrefset.py:72(__len__)
        6    0.000    0.000    0.000    0.000 typing.py:3324(<lambda>)
        1    0.000    0.000    0.000    0.000 calendar.py:630(LocaleHTMLCalendar)
        1    0.000    0.000    0.000    0.000 multipart.py:559(BodyPartReaderPayload)
        1    0.000    0.000    0.000    0.000 _make.py:2365(Attribute)
        1    0.000    0.000    0.000    0.000 locks.py:469(_BarrierState)
        1    0.000    0.000    0.000    0.000 request.py:910(AbstractBasicAuthHandler)
        1    0.000    0.000    0.000    0.000 argparse.py:1107(_HelpAction)
        1    0.000    0.000    0.000    0.000 compression_utils.py:148(ZLibCompressor)
        1    0.000    0.000    0.000    0.000 subprocess.py:135(wait)
        1    0.000    0.000    0.000    0.000 client.py:199(ClientTimeout)
        2    0.000    0.000    0.000    0.000 typing.py:1574(copy_with)
        1    0.000    0.000    0.000    0.000 http_exceptions.py:11(HttpProcessingError)
        1    0.000    0.000    0.000    0.000 runners.py:48(__init__)
        6    0.000    0.000    0.000    0.000 contextlib.py:481(_exit_wrapper)
        1    0.000    0.000    0.001    0.001 events.py:728(new_event_loop)
        4    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1370(_find_parent_path_names)
        1    0.000    0.000    0.000    0.000 ipaddress.py:2225(IPv6Interface)
        1    0.000    0.000    0.000    0.000 coroutines.py:10(_is_debug_mode)
       18    0.000    0.000    0.000    0.000 parser.py:49(set)
        1    0.000    0.000    0.000    0.000 http_parser.py:125(HeadersParser)
        1    0.000    0.000    0.000    0.000 validators.py:330(_DeepIterable)
        1    0.000    0.000    0.000    0.000 base_subprocess.py:264(_call_connection_lost)
        1    0.000    0.000    0.001    0.001 api.py:1(<module>)
        1    0.000    0.000    0.000    0.000 <frozen codecs>:94(__new__)
        1    0.000    0.000    0.000    0.000 payload.py:350(BytesPayload)
        1    0.000    0.000    0.001    0.001 events.py:835(new_event_loop)
        3    0.000    0.000    0.000    0.000 __init__.py:305(escape)
        1    0.000    0.000    0.000    0.000 resolver.py:85(AsyncResolver)
        1    0.000    0.000    0.000    0.000 weakref.py:414(__getitem__)
        1    0.000    0.000    0.000    0.000 <frozen codecs>:322(decode)
        1    0.000    0.000    0.000    0.000 timeouts.py:18(_State)
        1    0.000    0.000    0.000    0.000 argparse.py:1169(__init__)
        2    0.000    0.000    0.000    0.000 __init__.py:1120(__init__)
        1    0.000    0.000    0.000    0.000 transports.py:1(<module>)
        1    0.000    0.000    0.000    0.000 _abc.py:15(SupportsKeys)
        1    0.000    0.000    0.000    0.000 contextvars.py:1(<module>)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:663(_notify_content)
       10    0.000    0.000    0.000    0.000 argparse.py:205(__init__)
        1    0.000    0.000    0.000    0.000 _abc.py:55(ParserBase)
        1    0.000    0.000    0.000    0.000 base.py:16(LicenseType)
        1    0.000    0.000    0.000    0.000 sslproto.py:890(_set_read_buffer_limits)
        1    0.000    0.000    0.000    0.000 argparse.py:2285(_match_argument)
        1    0.000    0.000    0.000    0.000 main.py:258(_walk_to_root)
        1    0.000    0.000    0.000    0.000 queue.py:316(_PySimpleQueue)
        1    0.000    0.000    0.000    0.000 streams.py:40(AsyncStreamIterator)
        1    0.000    0.000    0.000    0.000 events.py:685(__init__)
        1    0.000    0.000    0.000    0.000 runners.py:20(Runner)
        6    0.000    0.000    0.000    0.000 threading.py:1147(daemon)
        1    0.000    0.000    0.000    0.000 socket.py:677(SocketIO)
        1    0.000    0.000    0.000    0.000 ipaddress.py:1430(IPv4Interface)
       12    0.000    0.000    0.000    0.000 {method 'get_loop' of '_asyncio.Future' objects}
        5    0.000    0.000    0.000    0.000 events.py:797(get_event_loop_policy)
        1    0.000    0.000    0.000    0.000 sslproto.py:31(AppProtocolState)
        1    0.000    0.000    0.000    0.000 parser.py:33(Binding)
        1    0.000    0.000    0.000    0.000 __init__.py:113(_)
        1    0.000    0.000    0.000    0.000 trsock.py:18(family)
        1    0.000    0.000    0.000    0.000 payload_streamer.py:35(_stream_wrapper)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1363(__init__)
        1    0.000    0.000    0.000    0.000 {built-in method posix.sysconf}
        2    0.000    0.000    0.000    0.000 base.py:62(__aenter__)
        1    0.000    0.000    0.000    0.000 payload.py:66(Order)
        3    0.000    0.000    0.000    0.000 {method 'get' of 'multidict._multidict.MultiDictProxy' objects}
        1    0.000    0.000    0.000    0.000 {built-in method math.exp}
        5    0.000    0.000    0.000    0.000 {method 'remove' of 'collections.deque' objects}
        1    0.000    0.000    0.000    0.000 runners.py:14(_State)
        1    0.000    0.000    0.000    0.000 variables.py:29(Literal)
        1    0.000    0.000    0.000    0.000 resolver.py:206(_DNSResolverManager)
        1    0.000    0.000    0.000    0.000 response.py:1(<module>)
        1    0.000    0.000    0.000    0.000 _url.py:902(raw_path_qs)
        1    0.000    0.000    0.000    0.000 connector.py:1467(_convert_hosts_to_addr_infos)
        2    0.000    0.000    0.000    0.000 lofi_manager.py:76(<lambda>)
        2    0.000    0.000    0.000    0.000 {method '_set_alpn_protocols' of '_ssl._SSLContext' objects}
        1    0.000    0.000    0.000    0.000 feedparser.py:136(FeedParser)
        1    0.000    0.000    0.000    0.000 subprocess.py:487(__init__)
       13    0.000    0.000    0.000    0.000 {method 'cancelled' of '_asyncio.Task' objects}
        1    0.000    0.000    0.000    0.000 timeouts.py:165(timeout_at)
        1    0.000    0.000    0.000    0.000 glob.py:335(__init__)
        3    0.000    0.000    0.002    0.001 dataclasses.py:1277(dataclass)
        4    0.000    0.000    0.000    0.000 enum.py:432(<genexpr>)
       15    0.000    0.000    0.000    0.000 {built-in method _warnings._filters_mutated}
        1    0.000    0.000    0.000    0.000 helpers.py:117(noop)
        3    0.000    0.000    0.000    0.000 {built-in method sys.get_asyncgen_hooks}
        1    0.000    0.000    0.000    0.000 constants.py:1(<module>)
        1    0.000    0.000    0.000    0.000 abc.py:129(ResolveResult)
        2    0.000    0.000    0.000    0.000 base_subprocess.py:198(<lambda>)
       11    0.000    0.000    0.000    0.000 enum.py:1456(<lambda>)
        5    0.000    0.000    0.000    0.000 {method 'count' of 'list' objects}
        1    0.000    0.000    0.000    0.000 sslproto.py:106(close)
        1    0.000    0.000    0.000    0.000 http_parser.py:117(ChunkState)
        6    0.000    0.000    0.000    0.000 {method 'rsplit' of 'str' objects}
        1    0.000    0.000    0.000    0.000 ssl.py:929(cipher)
        4    0.000    0.000    0.000    0.000 thread.py:48(__init__)
        1    0.000    0.000    0.000    0.000 _compression.py:1(<module>)
        1    0.000    0.000    0.000    0.000 {built-in method posix.confstr}
        1    0.000    0.000    0.000    0.000 abc.py:113(AbstractView)
        1    0.000    0.000    0.000    0.000 string.py:57(Template)
        1    0.000    0.000    0.000    0.000 random.py:225(__init_subclass__)
        1    0.000    0.000    0.000    0.000 client.py:1485(__aexit__)
        1    0.000    0.000    0.000    0.000 feedparser.py:46(BufferedSubFile)
        1    0.000    0.000    0.000    0.000 compression_utils.py:211(ZLibDecompressor)
        1    0.000    0.000    0.000    0.000 filters.py:1(<module>)
        1    0.000    0.000    0.000    0.000 typing.py:709(ClassVar)
        2    0.000    0.000    0.000    0.000 base_subprocess.py:292(connection_lost)
        7    0.000    0.000    0.000    0.000 {method 'strip' of 'str' objects}
        1    0.000    0.000    0.000    0.000 selectors.py:206(__exit__)
        2    0.000    0.000    0.000    0.000 validators.py:212(optional)
        2    0.000    0.000    0.000    0.000 base_subprocess.py:223(_pipe_connection_lost)
        5    0.000    0.000    0.000    0.000 threading.py:312(_release_save)
        1    0.000    0.000    0.000    0.000 argparse.py:2370(_get_option_tuples)
        1    0.000    0.000    0.000    0.000 thread.py:47(_WorkItem)
        1    0.000    0.000    0.000    0.000 {method 'getrandbits' of '_random.Random' objects}
        2    0.000    0.000    0.000    0.000 __init__.py:451(__init__)
        5    0.000    0.000    0.000    0.000 __init__.py:1302(__init__)
        1    0.000    0.000    0.000    0.000 variables.py:48(Variable)
        1    0.000    0.000    0.000    0.000 __init__.py:1493(setLevel)
        4    0.000    0.000    0.000    0.000 base_subprocess.py:137(get_pid)
        2    0.000    0.000    0.000    0.000 client_reqrep.py:180(_merge_ssl_params)
        1    0.000    0.000    0.000    0.000 request.py:396(OpenerDirector)
        3    0.000    0.000    0.000    0.000 dataclasses.py:879(_hash_set_none)
        4    0.000    0.000    0.000    0.000 argparse.py:2414(_get_nargs_pattern)
        1    0.000    0.000    0.000    0.000 _colorize.py:13(ANSIColors)
        4    0.000    0.000    0.000    0.000 {built-in method posix._path_normpath}
        1    0.000    0.000    0.000    0.000 _url.py:81(UndefinedType)
        1    0.000    0.000    0.000    0.000 _make.py:3030(_AndValidator)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:989(create_module)
        6    0.000    0.000    0.000    0.000 unix_events.py:891(__init_subclass__)
        1    0.000    0.000    0.000    0.000 threading.py:674(Barrier)
        2    0.000    0.000    0.000    0.000 {method 'unregister' of 'select.poll' objects}
        1    0.000    0.000    0.000    0.000 main.py:297(_is_debugger)
        7    0.000    0.000    0.000    0.000 {method 'result' of '_asyncio.Task' objects}
        1    0.000    0.000    0.000    0.000 protocols.py:1(<module>)
        1    0.000    0.000    0.000    0.000 trsock.py:1(<module>)
        1    0.000    0.000    0.000    0.000 locks.py:23(Lock)
        8    0.000    0.000    0.000    0.000 typing.py:2204(<genexpr>)
        4    0.000    0.000    0.000    0.000 futures.py:384(_call_check_cancel)
        1    0.000    0.000    0.000    0.000 decoder.py:262(JSONDecoder)
        3    0.000    0.000    0.000    0.000 client_reqrep.py:658(_cleanup_writer)
        1    0.000    0.000    0.000    0.000 client_proto.py:274(start_timeout)
        3    0.000    0.000    0.000    0.000 typing.py:341(_unpack_args)
       10    0.000    0.000    0.000    0.000 {method 'exception' of '_asyncio.Task' objects}
        1    0.000    0.000    0.000    0.000 events.py:29(Handle)
        4    0.000    0.000    0.000    0.000 {built-in method sys.audit}
        1    0.000    0.000    0.000    0.000 _url.py:723(raw_host)
        4    0.000    0.000    0.000    0.000 {method 'clear' of 'list' objects}
        1    0.000    0.000    0.000    0.000 unix_events.py:639(_UnixWritePipeTransport)
        1    0.000    0.000    0.000    0.000 parser.py:40(Position)
        1    0.000    0.000    0.000    0.000 _encoded_words.py:73(_QByteMap)
        6    0.000    0.000    0.000    0.000 {method 'pop' of 'collections.deque' objects}
        1    0.000    0.000    0.000    0.000 base_protocol.py:19(__init__)
       10    0.000    0.000    0.000    0.000 base_events.py:768(is_running)
        4    0.000    0.000    0.000    0.000 {built-in method builtins.vars}
        5    0.000    0.000    0.000    0.000 enum.py:1248(_generate_next_value_)
        1    0.000    0.000    0.000    0.000 unix_events.py:1481(__init__)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.client.ClientTimeout>:35(__init__)
        1    0.000    0.000    0.000    0.000 parse.py:220(_NetlocResultMixinBytes)
        1    0.000    0.000    0.000    0.000 request.py:1051(AbstractDigestAuthHandler)
        1    0.000    0.000    0.000    0.000 selector_events.py:759(_SelectorTransport)
        1    0.000    0.000    0.000    0.000 subprocess.py:131(returncode)
        1    0.000    0.000    0.000    0.000 streams.py:412(StreamReader)
        1    0.000    0.000    0.000    0.000 transports.py:19(get_extra_info)
        3    0.000    0.000    0.000    0.000 <frozen _collections_abc>:392(__subclasshook__)
        1    0.000    0.000    0.000    0.000 streams.py:60(ChunkTupleAsyncStreamIterator)
        2    0.000    0.000    0.000    0.000 __init__.py:1690(addHandler)
        4    0.000    0.000    0.000    0.000 <frozen abc>:146(update_abstractmethods)
        1    0.000    0.000    0.000    0.000 iterators.py:1(<module>)
       16    0.000    0.000    0.000    0.000 utils.py:48(_make_proxy_method)
        1    0.000    0.000    0.000    0.000 threading.py:1494(_register_atexit)
       10    0.000    0.000    0.000    0.000 __future__.py:83(__init__)
        4    0.000    0.000    0.000    0.000 {built-in method _stat.S_ISFIFO}
        1    0.000    0.000    0.000    0.000 _local.py:245(_from_parsed_parts)
        1    0.000    0.000    0.000    0.000 client.py:1454(HTTPSConnection)
        1    0.000    0.000    0.000    0.000 streams.py:180(StreamReaderProtocol)
        8    0.000    0.000    0.000    0.000 <frozen _collections_abc>:850(__init__)
        4    0.000    0.000    0.000    0.000 <frozen os>:811(getenv)
       10    0.000    0.000    0.000    0.000 utils.py:4(delegate_to_executor)
        1    0.000    0.000    0.000    0.000 sslproto.py:385(connection_made)
        1    0.000    0.000    0.000    0.000 {method 'strip' of 'bytes' objects}
        1    0.000    0.000    0.000    0.000 connector.py:123(__await__)
        1    0.000    0.000    0.000    0.000 base.py:102(SearchResult)
        1    0.000    0.000    0.000    0.000 base.py:7(AsyncBase)
        1    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_sha224}
        6    0.000    0.000    0.000    0.000 {method 'isdisjoint' of 'set' objects}
        1    0.000    0.000    0.000    0.000 trsock.py:73(setsockopt)
        1    0.000    0.000    0.000    0.000 models.py:72(WebSocketError)
        1    0.000    0.000    0.000    0.000 subprocess.py:169(TimeoutExpired)
        5    0.000    0.000    0.000    0.000 {method 'has_default' of 'typing.TypeVar' objects}
        6    0.000    0.000    0.000    0.000 typing.py:1478(<genexpr>)
        1    0.000    0.000    0.000    0.000 main.py:35(__init__)
        1    0.000    0.000    0.000    0.000 <string>:1(__create_fn__)
        1    0.000    0.000    0.000    0.000 cookies.py:444(BaseCookie)
        1    0.000    0.000    0.000    0.000 <frozen codecs>:312(__init__)
        2    0.000    0.000    0.000    0.000 parser.py:45(start)
        5    0.000    0.000    0.000    0.000 {method 'fileno' of '_socket.socket' objects}
        4    0.000    0.000    0.000    0.000 {built-in method maketrans}
        1    0.000    0.000    0.000    0.000 _policybase.py:18(_PolicyBase)
        1    0.000    0.000    0.000    0.000 parse.py:361(_fix_result_transcoding)
        1    0.000    0.000    0.000    0.000 locks.py:219(Condition)
        1    0.000    0.000    0.000    0.000 ssl.py:227(_TLSMessageType)
       12    0.000    0.000    0.000    0.000 _local.py:298(root)
        5    0.000    0.000    0.000    0.000 {method 'bit_length' of 'int' objects}
        1    0.000    0.000    0.000    0.000 http_parser.py:566(HttpRequestParser)
        4    0.000    0.000    0.000    0.000 lofi_manager.py:102(<genexpr>)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:110(ContentDisposition)
        1    0.000    0.000    0.000    0.000 ssl.py:159(TLSVersion)
        1    0.000    0.000    0.000    0.000 connector.py:116(_DeprecationWaiter)
        1    0.000    0.000    0.000    0.000 subprocess.py:104(process_exited)
        1    0.000    0.000    0.000    0.000 ssl.py:185(_TLSAlertType)
        3    0.000    0.000    0.000    0.000 selector_events.py:835(is_closing)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.client_ws.ClientWSTimeout>:29(__init__)
        1    0.000    0.000    0.000    0.000 argparse.py:298(_format_usage)
        1    0.000    0.000    0.000    0.000 weakref.py:347(__new__)
        1    0.000    0.000    0.000    0.000 tasks.py:570(_AsCompletedIterator)
        8    0.000    0.000    0.000    0.000 {built-in method math.floor}
        1    0.000    0.000    0.000    0.000 _parseaddr.py:214(AddrlistClass)
        8    0.000    0.000    0.000    0.000 {built-in method _asyncio._get_running_loop}
        1    0.000    0.000    0.000    0.000 _make.py:2034(_assign_with_converter)
        1    0.000    0.000    0.000    0.000 platform.py:870(_Processor)
        1    0.000    0.000    0.000    0.000 warnings.py:518(deprecated)
        1    0.000    0.000    0.000    0.000 payload.py:82(payload_type)
        1    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_shake_128}
        2    0.000    0.000    0.000    0.000 lofi_manager.py:183(<lambda>)
        2    0.000    0.000    0.000    0.000 sslproto.py:61(add_flowcontrol_defaults)
        3    0.000    0.000    0.000    0.000 <frozen _collections_abc>:313(__subclasshook__)
        1    0.000    0.000    0.000    0.000 validators.py:420(_NumberValidator)
        1    0.000    0.000    0.000    0.000 connector.py:206(_notify_release)
        1    0.000    0.000    0.000    0.000 parser.py:16(Parser)
        1    0.000    0.000    0.000    0.000 ipaddress.py:1497(IPv4Network)
        1    0.000    0.000    0.000    0.000 _url.py:1486(_encode_host)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1024(update_host)
        3    0.000    0.000    0.000    0.000 sslproto.py:896(_get_read_buffer_size)
        1    0.000    0.000    0.000    0.000 transports.py:9(BaseTransport)
        1    0.000    0.000    0.000    0.000 _policybase.py:279(Compat32)
        1    0.000    0.000    0.000    0.000 glob.py:414(wildcard_selector)
        1    0.000    0.000    0.000    0.000 _compression.py:33(DecompressReader)
        1    0.000    0.000    0.000    0.000 payload.py:920(JsonPayload)
        1    0.000    0.000    0.000    0.000 _abc.py:43(_is_case_sensitive)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:997(exec_module)
        1    0.000    0.000    0.000    0.000 tempfile.py:432(_TemporaryFileCloser)
        3    0.000    0.000    0.000    0.000 _parser.py:294(seek)
        1    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_sha3_256}
        1    0.000    0.000    0.000    0.000 argparse.py:1353(_ActionsContainer)
        1    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_sha512}
        2    0.000    0.000    0.000    0.000 argparse.py:293(_join_parts)
        2    0.000    0.000    0.000    0.000 helpers.py:642(assert_timeout)
        1    0.000    0.000    0.000    0.000 _url.py:773(host_port_subcomponent)
        1    0.000    0.000    0.000    0.000 helpers.py:312(MimeType)
        1    0.000    0.000    0.000    0.000 header.py:408(_ValueFormatter)
        8    0.000    0.000    0.000    0.000 variables.py:30(__init__)
        4    0.000    0.000    0.000    0.000 {method 'cast' of 'memoryview' objects}
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.client_ws.ClientWSTimeout>:1(<module>)
        1    0.000    0.000    0.000    0.000 payload.py:96(PayloadRegistry)
        1    0.000    0.000    0.000    0.000 ssl.py:940(compression)
        1    0.000    0.000    0.000    0.000 main.py:287(_is_interactive)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:645(_wait_released)
        1    0.000    0.000    0.000    0.000 base.py:131(__init__)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:1103(_resolve_filename)
        1    0.000    0.000    0.000    0.000 subprocess.py:118(Process)
        1    0.000    0.000    0.000    0.000 weakref.py:352(__init__)
        1    0.000    0.000    0.000    0.000 response.py:68(addinfourl)
        1    0.000    0.000    0.000    0.000 validators.py:667(_OrValidator)
        1    0.000    0.000    0.000    0.000 streams.py:116(FlowControlMixin)
        3    0.000    0.000    0.000    0.000 {method 'rfind' of 'bytes' objects}
        1    0.000    0.000    0.000    0.000 tempfile.py:132(_RandomNameSequence)
        1    0.000    0.000    0.000    0.000 tracing.py:225(TraceRequestChunkSentParams)
        1    0.000    0.000    0.000    0.000 helpers.py:636(__init__)
        1    0.000    0.000    0.000    0.000 base_events.py:2063(set_debug)
        5    0.000    0.000    0.000    0.000 {built-in method builtins.iter}
        3    0.000    0.000    0.000    0.000 payload.py:83(__init__)
        1    0.000    0.000    0.000    0.000 selectors.py:281(SelectSelector)
        4    0.000    0.000    0.000    0.000 base_events.py:558(_check_default_executor)
        1    0.000    0.000    0.000    0.000 header.py:175(Header)
        1    0.000    0.000    0.000    0.000 validators.py:233(_InValidator)
        4    0.000    0.000    0.000    0.000 signal.py:24(_int_to_enum)
        2    0.000    0.000    0.000    0.000 _query.py:18(query_var)
        1    0.000    0.000    0.000    0.000 validators.py:359(deep_iterable)
        2    0.000    0.000    0.000    0.000 {built-in method posix.waitstatus_to_exitcode}
        1    0.000    0.000    0.000    0.000 _url.py:251(from_parts_uncached)
        4    0.000    0.000    0.000    0.000 argparse.py:2568(_check_value)
        1    0.000    0.000    0.000    0.000 manager.py:124(add_source)
        1    0.000    0.000    0.000    0.000 tempfile.py:475(_TemporaryFileWrapper)
        1    0.000    0.000    0.000    0.000 _base.py:569(Executor)
        9    0.000    0.000    0.000    0.000 utils.py:62(_make_cond_delegate_method)
        1    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_sha256}
        1    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_sha384}
        5    0.000    0.000    0.000    0.000 {method 'cancelling' of '_asyncio.Task' objects}
        3    0.000    0.000    0.000    0.000 argparse.py:1888(_get_positional_actions)
        1    0.000    0.000    0.000    0.000 _base.py:74(_AsCompletedWaiter)
        1    0.000    0.000    0.000    0.000 pickle.py:194(_Framer)
        1    0.000    0.000    0.001    0.001 argparse.py:1897(parse_args)
        1    0.000    0.000    0.001    0.001 <frozen importlib._bootstrap>:1171(exec_module)
        1    0.000    0.000    0.000    0.000 base_protocol.py:71(connection_lost)
        1    0.000    0.000    0.000    0.000 __init__.py:511(StringTemplateStyle)
        2    0.000    0.000    0.000    0.000 dataclasses.py:646(<genexpr>)
        1    0.000    0.000    0.000    0.000 tracing.py:216(TraceRequestStartParams)
        1    0.000    0.000    0.000    0.000 compression_utils.py:32(ZLibDecompressObjProtocol)
        1    0.000    0.000    0.000    0.000 _url.py:90(CacheInfo)
        1    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_sha3_384}
        1    0.000    0.000    0.000    0.000 transports.py:253(_FlowControlMixin)
        1    0.000    0.000    0.000    0.000 locks.py:158(Event)
        1    0.000    0.000    0.000    0.000 string.py:188(Formatter)
        1    0.000    0.000    0.000    0.000 _weakrefset.py:53(_commit_removals)
        1    0.000    0.000    0.000    0.000 {method 'getpeername' of '_socket.socket' objects}
        2    0.000    0.000    0.000    0.000 {method 'remove' of 'set' objects}
        5    0.000    0.000    0.000    0.000 base_subprocess.py:259(<genexpr>)
        4    0.000    0.000    0.000    0.000 <string>:1(<lambda>)
        1    0.000    0.000    0.000    0.000 __init__.py:286(LogRecord)
        1    0.000    0.000    0.000    0.000 request.py:1450(FileHandler)
        1    0.000    0.000    0.000    0.000 helpers.py:765(ErrorableProtocol)
        1    0.000    0.000    0.000    0.000 unix_events.py:1255(MultiLoopChildWatcher)
        1    0.000    0.000    0.000    0.000 connector.py:689(_get)
        1    0.000    0.000    0.000    0.000 compression_utils.py:136(ZlibBaseHandler)
        4    0.000    0.000    0.000    0.000 {method 'keys' of 'dict' objects}
        1    0.000    0.000    0.000    0.000 charset.py:162(Charset)
        1    0.000    0.000    0.000    0.000 helpers.py:237(ProxyInfo)
        1    0.000    0.000    0.000    0.000 encoder.py:74(JSONEncoder)
        2    0.000    0.000    0.000    0.000 streams.py:485(feed_eof)
        1    0.000    0.000    0.000    0.000 {built-in method select.poll}
        1    0.000    0.000    0.000    0.000 request.py:615(HTTPRedirectHandler)
        1    0.000    0.000    0.000    0.000 base.py:116(RateLimitError)
        1    0.000    0.000    0.000    0.000 sslproto.py:353(_set_app_protocol)
        1    0.000    0.000    0.000    0.000 abc.py:177(__init__)
        1    0.000    0.000    0.000    0.000 unix_events.py:1012(BaseChildWatcher)
        1    0.000    0.000    0.000    0.000 decoder.py:20(JSONDecodeError)
        1    0.000    0.000    0.000    0.000 _weakrefset.py:21(__enter__)
        1    0.000    0.000    0.000    0.000 ssl.py:665(verify_flags)
        4    0.000    0.000    0.000    0.000 base_subprocess.py:286(connection_made)
        1    0.000    0.000    0.000    0.000 argparse.py:1157(_SubParsersAction)
        5    0.000    0.000    0.000    0.000 functools.py:1001(__set_name__)
        1    0.000    0.000    0.000    0.000 unix_events.py:954(PidfdChildWatcher)
        2    0.000    0.000    0.000    0.000 inspect.py:3089(<genexpr>)
        1    0.000    0.000    0.000    0.000 calendar.py:449(HTMLCalendar)
        1    0.000    0.000    0.000    0.000 __init__.py:815(Filterer)
        4    0.000    0.000    0.000    0.000 _base.py:398(__get_result)
        1    0.000    0.000    0.000    0.000 request.py:1227(AbstractHTTPHandler)
        1    0.000    0.000    0.000    0.000 streams.py:687(FlowControlDataQueue)
        1    0.000    0.000    0.000    0.000 validators.py:198(_OptionalValidator)
        1    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_sha3_512}
        1    0.000    0.000    0.000    0.000 threading.py:141(_RLock)
        1    0.000    0.000    0.000    0.000 __init__.py:1190(FileHandler)
        1    0.000    0.000    0.000    0.000 _weakrefset.py:17(__init__)
        1    0.000    0.000    0.000    0.000 validators.py:128(_MatchesReValidator)
        1    0.000    0.000    0.000    0.000 lofi_manager.py:41(configure_sources)
        1    0.000    0.000    0.000    0.000 _parse.py:108(split_netloc)
        1    0.000    0.000    0.000    0.000 _make.py:2867(_fmt_converter_call)
        1    0.000    0.000    0.000    0.000 events.py:171(AbstractServer)
        4    0.000    0.000    0.000    0.000 {method 'upper' of 'str' objects}
        4    0.000    0.000    0.000    0.000 {method 'fileno' of '_io.FileIO' objects}
        1    0.000    0.000    0.000    0.000 __init__.py:736(BufferingFormatter)
        1    0.000    0.000    0.000    0.000 payload_streamer.py:50(streamer)
        1    0.000    0.000    0.000    0.000 client_middleware_digest_auth.py:35(DigestAuthChallenge)
        1    0.000    0.000    0.000    0.000 validators.py:90(_InstanceOfValidator)
        4    0.000    0.000    0.000    0.000 unix_events.py:534(is_reading)
        1    0.000    0.000    0.000    0.000 {method 'cipher' of '_ssl._SSLSocket' objects}
        1    0.000    0.000    0.000    0.000 http_exceptions.py:45(BadHttpMessage)
        1    0.000    0.000    0.000    0.000 base_events.py:576(shutdown_asyncgens)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.client.ClientTimeout>:1(<module>)
        1    0.000    0.000    0.000    0.000 _make.py:89(_CacheHashWrapper)
        1    0.000    0.000    0.000    0.000 {built-in method _hashlib.openssl_shake_256}
        1    0.000    0.000    0.000    0.000 selectors.py:60(_SelectorMapping)
        1    0.000    0.000    0.000    0.000 ssl.py:669(verify_mode)
        4    0.000    0.000    0.000    0.000 {method '_is_owned' of '_thread.RLock' objects}
        1    0.000    0.000    0.000    0.000 http_exceptions.py:87(InvalidHeader)
        1    0.000    0.000    0.000    0.000 functools.py:677(cache)
        2    0.000    0.000    0.000    0.000 {built-in method _thread.daemon_threads_allowed}
        1    0.000    0.000    0.000    0.000 __init__.py:1848(__init__)
        1    0.000    0.000    0.000    0.000 helpers.py:449(is_ip_address)
        8    0.000    0.000    0.000    0.000 {method 'random' of '_random.Random' objects}
        6    0.000    0.000    0.000    0.000 {built-in method _asyncio._set_running_loop}
        1    0.000    0.000    0.000    0.000 tracing.py:243(TraceRequestEndParams)
        1    0.000    0.000    0.000    0.000 helpers.py:560(__init__)
        2    0.000    0.000    0.000    0.000 {method 'clear' of 'collections.deque' objects}
        1    0.000    0.000    0.000    0.000 request.py:1354(HTTPSHandler)
        1    0.000    0.000    0.000    0.000 selector_events.py:838(is_reading)
        1    0.000    0.000    0.000    0.000 tracing.py:253(TraceRequestExceptionParams)
        1    0.000    0.000    0.000    0.000 typedefs.py:63(Middleware)
        8    0.000    0.000    0.000    0.000 utils.py:22(proxy_property_directly)
        1    0.000    0.000    0.000    0.000 parser.py:28(Original)
        1    0.000    0.000    0.000    0.000 <string>:2(__init__)
        2    0.000    0.000    0.000    0.000 {built-in method math.log}
        4    0.000    0.000    0.000    0.000 _parser.py:165(__delitem__)
        1    0.000    0.000    0.000    0.000 connector.py:731(_release_waiter)
        1    0.000    0.000    0.000    0.000 selector_events.py:1208(_SelectorDatagramTransport)
        3    0.000    0.000    0.000    0.000 argparse.py:946(__call__)
       12    0.000    0.000    0.000    0.000 _local.py:307(_tail)
        1    0.000    0.000    0.000    0.000 {method 'copy' of 'list' objects}
        1    0.000    0.000    0.000    0.000 streams.py:644(set_exception)
        1    0.000    0.000    0.000    0.000 subprocess.py:17(SubprocessStreamProtocol)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1170(update_body_from_data)
        4    0.000    0.000    0.000    0.000 _url.py:139(rewrite_module)
        1    0.000    0.000    0.000    0.000 _compression.py:9(BaseStream)
        1    0.000    0.000    0.000    0.000 __init__.py:1242(_open)
        1    0.000    0.000    0.000    0.000 helpers.py:916(ETag)
        1    0.000    0.000    0.000    0.000 parser.py:75(BytesParser)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:1158(create_module)
        4    0.000    0.000    0.000    0.000 streams.py:476(set_transport)
        1    0.000    0.000    0.000    0.000 argparse.py:256(add_usage)
        3    0.000    0.000    0.000    0.000 __init__.py:1354(disable)
        1    0.000    0.000    0.000    0.000 __init__.py:1272(_StderrHandler)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:124(RequestInfo)
        1    0.000    0.000    0.000    0.000 validators.py:577(_subclass_of)
        1    0.000    0.000    0.000    0.000 unix_events.py:1477(_UnixDefaultEventLoopPolicy)
        1    0.000    0.000    0.000    0.000 response.py:14(addbase)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:231(UnixClientConnectorError)
        1    0.000    0.000    0.000    0.000 events.py:632(AbstractEventLoopPolicy)
        1    0.000    0.000    0.000    0.000 warnings.py:442(catch_warnings)
        2    0.000    0.000    0.000    0.000 {method 'register' of 'select.poll' objects}
        1    0.000    0.000    0.000    0.000 request.py:879(HTTPPasswordMgrWithPriorAuth)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:258(ServerDisconnectedError)
        1    0.000    0.000    0.000    0.000 validators.py:556(_SubclassOfValidator)
        1    0.000    0.000    0.000    0.000 queues.py:282(PriorityQueue)
        1    0.000    0.000    0.000    0.000 {built-in method _socket.inet_pton}
        1    0.000    0.000    0.000    0.000 __init__.py:1343(__init__)
        2    0.000    0.000    0.000    0.000 {method 'split' of 'bytes' objects}
        1    0.000    0.000    0.000    0.000 tracing.py:326(TraceRequestHeadersSentParams)
        2    0.000    0.000    0.000    0.000 {built-in method _signal.getsignal}
        8    0.000    0.000    0.000    0.000 variables.py:44(resolve)
        2    0.000    0.000    0.000    0.000 connector.py:559(closed)
        3    0.000    0.000    0.000    0.000 dataclasses.py:412(<genexpr>)
        1    0.000    0.000    0.000    0.000 tracing.py:263(TraceRequestRedirectParams)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.helpers.ETag>:1(<module>)
        1    0.000    0.000    0.000    0.000 encoder.py:105(__init__)
        1    0.000    0.000    0.000    0.000 validators.py:498(_MaxLengthValidator)
        6    0.000    0.000    0.000    0.000 subprocess.py:1327(_on_error_fd_closer)
        1    0.000    0.000    0.000    0.000 validators.py:527(_MinLengthValidator)
        1    0.000    0.000    0.000    0.000 _abc.py:20(SupportsIKeys)
        1    0.000    0.000    0.000    0.000 connector.py:119(__init__)
        1    0.000    0.000    0.000    0.000 unix_events.py:1146(FastChildWatcher)
        1    0.000    0.000    0.000    0.000 selectors.py:426(EpollSelector)
        1    0.000    0.000    0.000    0.000 unix_events.py:1465(can_use_pidfd)
        1    0.000    0.000    0.000    0.000 tasks.py:755(_GatheringFuture)
        6    0.000    0.000    0.000    0.000 signal.py:50(_wraps)
        1    0.000    0.000    0.000    0.000 tracing.py:234(TraceResponseChunkReceivedParams)
        1    0.000    0.000    0.000    0.000 request.py:1345(HTTPHandler)
        1    0.000    0.000    0.000    0.000 connector.py:567(_available_connections)
        1    0.000    0.000    0.000    0.000 random.py:880(SystemRandom)
        6    0.000    0.000    0.000    0.000 threading.py:605(is_set)
        1    0.000    0.000    0.000    0.000 ssl.py:170(_TLSContentType)
        1    0.000    0.000    0.000    0.000 payload_streamer.py:72(StreamPayload)
        3    0.000    0.000    0.000    0.000 {method 'remove_done_callback' of '_asyncio.Task' objects}
        1    0.000    0.000    0.000    0.000 sslproto.py:855(_set_write_buffer_limits)
        2    0.000    0.000    0.000    0.000 inspect.py:386(isfunction)
        3    0.000    0.000    0.000    0.000 subprocess.py:1987(_internal_poll)
        1    0.000    0.000    0.000    0.000 _make.py:2744(Factory)
        5    0.000    0.000    0.000    0.000 connector.py:198(protocol)
        2    0.000    0.000    0.000    0.000 helpers.py:118(__await__)
        5    0.000    0.000    0.000    0.000 {method 'mro' of 'type' objects}
        1    0.000    0.000    0.000    0.000 payload.py:911(BufferedReaderPayload)
        1    0.000    0.000    0.000    0.000 request.py:1374(HTTPCookieProcessor)
        1    0.000    0.000    0.000    0.000 client.py:1192(_prepare_headers)
        1    0.000    0.000    0.000    0.000 __init__.py:2277(NullHandler)
        1    0.000    0.000    0.000    0.000 selectors.py:210(_BaseSelectorImpl)
        1    0.000    0.000    0.000    0.000 selectors.py:414(PollSelector)
        1    0.000    0.000    0.000    0.000 __init__.py:176(HTTPMethod)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._DeepIterable>:1(<module>)
        4    0.000    0.000    0.000    0.000 subprocess.py:1255(_remaining_time)
        1    0.000    0.000    0.000    0.000 compression_utils.py:27(ZLibCompressObjProtocol)
        1    0.000    0.000    0.000    0.000 __init__.py:1278(__init__)
        5    0.000    0.000    0.000    0.000 <frozen _collections_abc>:411(__subclasshook__)
        3    0.000    0.000    0.000    0.000 {built-in method _operator.index}
        4    0.000    0.000    0.000    0.000 {built-in method builtins.abs}
        1    0.000    0.000    0.000    0.000 http_exceptions.py:95(BadStatusLine)
        1    0.000    0.000    0.000    0.000 traceback.py:974(_ExceptionPrintContext)
        1    0.000    0.000    0.000    0.000 http_exceptions.py:104(BadHttpMethod)
        1    0.000    0.000    0.000    0.000 {built-in method _codecs.utf_8_decode}
        1    0.000    0.000    0.000    0.000 transports.py:316(_set_write_buffer_limits)
        1    0.000    0.000    0.000    0.000 client.py:1462(__init__)
        1    0.000    0.000    0.000    0.000 mixins.py:9(_LoopBoundMixin)
        1    0.000    0.000    0.000    0.000 http_writer.py:39(HttpVersion)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:117(_RequestInfo)
        1    0.000    0.000    0.000    0.000 tracing.py:305(TraceDnsResolveHostEndParams)
        1    0.000    0.000    0.000    0.000 exceptions.py:8(FrozenError)
        6    0.000    0.000    0.000    0.000 utils.py:13(proxy_method_directly)
        1    0.000    0.000    0.000    0.000 transports.py:199(SubprocessTransport)
        1    0.000    0.000    0.000    0.000 __init__.py:1296(PlaceHolder)
        1    0.000    0.000    0.000    0.000 tracing.py:298(TraceDnsResolveHostStartParams)
        1    0.000    0.000    0.000    0.000 resolver.py:34(__init__)
        1    0.000    0.000    0.000    0.000 parse.py:190(_NetlocResultMixinStr)
        1    0.000    0.000    0.000    0.000 streams.py:183(set_exception)
        1    0.000    0.000    0.000    0.000 converters.py:66(default_if_none)
        2    0.000    0.000    0.000    0.000 threading.py:1545(main_thread)
        1    0.000    0.000    0.000    0.000 calendar.py:200(__init__)
        7    0.000    0.000    0.000    0.000 argparse.py:1805(identity)
        3    0.000    0.000    0.000    0.000 {built-in method _abc.get_cache_token}
        1    0.000    0.000    0.000    0.000 payload.py:104(__init__)
        1    0.000    0.000    0.000    0.000 helpers.py:607(BaseTimerContext)
        1    0.000    0.000    0.000    0.000 idna.py:178(Codec)
        1    0.000    0.000    0.000    0.000 connector.py:237(__init__)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:768(__aenter__)
        1    0.000    0.000    0.000    0.000 http_exceptions.py:77(LineTooLong)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._DeepMapping>:1(<module>)
        5    0.000    0.000    0.000    0.000 {built-in method _stat.S_ISDIR}
        1    0.000    0.000    0.000    0.000 connector.py:1226(_get_fingerprint)
        1    0.000    0.000    0.000    0.000 helpers.py:573(register)
        1    0.000    0.000    0.000    0.000 request.py:762(ProxyHandler)
        1    0.000    0.000    0.000    0.000 base_subprocess.py:278(WriteSubprocessPipeProto)
        1    0.000    0.000    0.000    0.000 queue.py:281(PriorityQueue)
        2    0.000    0.000    0.000    0.000 calendar.py:120(__init__)
        1    0.000    0.000    0.000    0.000 tracing.py:312(TraceDnsCacheHitParams)
        1    0.000    0.000    0.000    0.000 selector_events.py:867(__del__)
        1    0.000    0.000    0.000    0.000 protocols.py:9(BaseProtocol)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceDnsCacheMissParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1034(update_version)
        1    0.000    0.000    0.000    0.000 pickle.py:257(_Unframer)
        1    0.000    0.000    0.000    0.000 payload_streamer.py:63(StreamWrapperPayload)
        1    0.000    0.000    0.000    0.000 errors.py:97(NonPrintableDefect)
        1    0.000    0.000    0.000    0.000 streams.py:635(is_eof)
        2    0.000    0.000    0.000    0.000 subprocess.py:1263(_check_timeout)
        1    0.000    0.000    0.000    0.000 connector.py:454(_cleanup_closed)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1291(update_expect_continue)
        1    0.000    0.000    0.000    0.000 request.py:1392(UnknownHandler)
        1    0.000    0.000    0.000    0.000 base.py:53(__init__)
        1    0.000    0.000    0.000    0.000 connector.py:809(__init__)
        1    0.000    0.000    0.000    0.000 connector.py:167(__del__)
        2    0.000    0.000    0.000    0.000 {built-in method _asyncio.get_event_loop}
        1    0.000    0.000    0.000    0.000 typing.py:2754(final)
        1    0.000    0.000    0.000    0.000 request.py:575(BaseHandler)
        1    0.000    0.000    0.000    0.000 _compat.py:35(_AnnotationExtractor)
        1    0.000    0.000    0.000    0.000 request.py:1507(FTPHandler)
        2    0.000    0.000    0.000    0.000 contextlib.py:450(__exit__)
        1    0.000    0.000    0.000    0.000 _local.py:839(PosixPath)
        1    0.000    0.000    0.000    0.000 connector.py:392(force_close)
        1    0.000    0.000    0.000    0.000 _base.py:96(_FirstCompletedWaiter)
        1    0.000    0.000    0.000    0.000 __init__.py:1842(RootLogger)
        1    0.000    0.000    0.000    0.000 compression_utils.py:258(BrotliDecompressor)
        1    0.000    0.000    0.000    0.000 <frozen os>:856(fsdecode)
        1    0.000    0.000    0.000    0.000 request.py:1021(HTTPBasicAuthHandler)
        1    0.000    0.000    0.000    0.000 threading.py:449(Semaphore)
        1    0.000    0.000    0.000    0.000 sslproto.py:120(__del__)
        3    0.000    0.000    0.000    0.000 base_events.py:727(stop)
        1    0.000    0.000    0.000    0.000 calendar.py:32(IllegalMonthError)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1303(update_proxy)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.helpers.ProxyInfo>:1(<module>)
        1    0.000    0.000    0.000    0.000 {method 'set_exception' of '_asyncio.Future' objects}
        1    0.000    0.000    0.000    0.000 calendar.py:609(LocaleTextCalendar)
        5    0.000    0.000    0.000    0.000 platform.py:920(_unknown_as_blank)
        1    0.000    0.000    0.000    0.000 validators.py:111(instance_of)
        1    0.000    0.000    0.000    0.000 inspect.py:2815(annotation)
        1    0.000    0.000    0.000    0.000 tracing.py:319(TraceDnsCacheMissParams)
        1    0.000    0.000    0.000    0.000 multipart.py:72(BadContentDispositionHeader)
        1    0.000    0.000    0.000    0.000 locks.py:451(BoundedSemaphore)
        1    0.000    0.000    0.000    0.000 __init__.py:325(AiofilesContextManagerTempDir)
        1    0.000    0.000    0.000    0.000 exceptions.py:29(IncompleteReadError)
        1    0.000    0.000    0.000    0.000 _url.py:811(port)
        1    0.000    0.000    0.000    0.000 _config.py:1(<module>)
        1    0.000    0.000    0.000    0.000 queues.py:298(LifoQueue)
        1    0.000    0.000    0.000    0.000 _base.py:141(_AcquireFutures)
        1    0.000    0.000    0.000    0.000 client.py:183(HTTPMessage)
        1    0.000    0.000    0.000    0.000 argparse.py:752(Action)
        3    0.000    0.000    0.000    0.000 dataclasses.py:429(__init__)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceDnsResolveHostStartParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 argparse.py:107(_AttributeHolder)
        4    0.000    0.000    0.000    0.000 unix_events.py:601(close)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceRequestExceptionParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 parse.py:323(DefragResult)
        1    0.000    0.000    0.000    0.000 base.py:176(RateLimiter)
        1    0.000    0.000    0.000    0.000 _parser.py:99(checkgroup)
        2    0.000    0.000    0.000    0.000 parser.py:41(__init__)
        1    0.000    0.000    0.000    0.000 helpers.py:185(strip_auth_from_url)
        1    0.000    0.000    0.000    0.000 compression_utils.py:68(CompressObjArgs)
        2    0.000    0.000    0.000    0.000 validators.py:294(__call__)
        1    0.000    0.000    0.000    0.000 temptypes.py:12(AsyncSpooledTemporaryFile)
        2    0.000    0.000    0.000    0.000 base.py:72(__post_init__)
        1    0.000    0.000    0.000    0.000 payload.py:1085(StreamReaderPayload)
        1    0.000    0.000    0.000    0.000 base_protocol.py:32(writing_paused)
        1    0.000    0.000    0.000    0.000 streams.py:151(connection_lost)
        1    0.000    0.000    0.000    0.000 tasks.py:975(_outer_done_callback)
        1    0.000    0.000    0.000    0.000 netrc.py:10(NetrcParseError)
        1    0.000    0.000    0.000    0.000 uuid.py:81(SafeUUID)
        1    0.000    0.000    0.000    0.000 parse.py:136(_ResultMixinStr)
        1    0.000    0.000    0.000    0.000 base.py:48(AiofilesContextManager)
        1    0.000    0.000    0.000    0.000 {method 'items' of 'collections.OrderedDict' objects}
        1    0.000    0.000    0.000    0.000 request.py:611(HTTPDefaultErrorHandler)
        1    0.000    0.000    0.000    0.000 __init__.py:81(_make_getattr)
        1    0.000    0.000    0.000    0.000 netrc.py:66(netrc)
        1    0.000    0.000    0.000    0.000 exceptions.py:81(NotCallableError)
        1    0.000    0.000    0.000    0.000 _local.py:462(PurePosixPath)
        3    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap>:1014(is_package)
        1    0.000    0.000    0.000    0.000 warnings.py:420(WarningMessage)
        1    0.000    0.000    0.000    0.000 threading.py:1320(Timer)
        1    0.000    0.000    0.000    0.000 validators.py:202(__call__)
        1    0.000    0.000    0.000    0.000 {built-in method from_iterable}
        1    0.000    0.000    0.000    0.000 parser.py:67(HeaderParser)
        6    0.000    0.000    0.000    0.000 base.py:43(_file)
        1    0.000    0.000    0.000    0.000 argparse.py:1128(_VersionAction)
        1    0.000    0.000    0.000    0.000 payload.py:62(LookupError)
        1    0.000    0.000    0.000    0.000 threading.py:536(BoundedSemaphore)
        1    0.000    0.000    0.000    0.000 _make.py:284(_Attributes)
        1    0.000    0.000    0.000    0.000 transports.py:46(ReadTransport)
        1    0.000    0.000    0.000    0.000 request.py:594(HTTPErrorProcessor)
        1    0.000    0.000    0.000    0.000 feedparser.py:533(BytesFeedParser)
        1    0.000    0.000    0.000    0.000 unix_events.py:844(_UnixSubprocessTransport)
        4    0.000    0.000    0.000    0.000 {built-in method _sre.ascii_tolower}
        3    0.000    0.000    0.000    0.000 {built-in method _stat.S_ISREG}
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceRequestStartParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceRequestRedirectParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.helpers.MimeType>:1(<module>)
        1    0.000    0.000    0.000    0.000 shutil.py:67(Error)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceDnsResolveHostEndParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:669(wait_for_close)
        1    0.000    0.000    0.000    0.000 cookies.py:584(SimpleCookie)
        1    0.000    0.000    0.000    0.000 validators.py:292(_IsCallableValidator)
        1    0.000    0.000    0.000    0.000 selector_events.py:828(set_protocol)
        1    0.000    0.000    0.000    0.000 calendar.py:586(different_locale)
        1    0.000    0.000    0.000    0.000 errors.py:37(MessageDefect)
        2    0.000    0.000    0.000    0.000 _make.py:2859(_get_global_name)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._MatchesReValidator>:1(<module>)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._MinLengthValidator>:1(<module>)
        1    0.000    0.000    0.000    0.000 trsock.py:15(__init__)
        1    0.000    0.000    0.000    0.000 argparse.py:1714(_MutuallyExclusiveGroup)
        1    0.000    0.000    0.000    0.000 __init__.py:1358(disable)
        1    0.000    0.000    0.000    0.000 subprocess.py:2180(_save_input)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceConnectionQueuedEndParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 {built-in method sys.is_finalizing}
        1    0.000    0.000    0.000    0.000 netrc.py:22(_netrclex)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr._version_info.VersionInfo>:1(<module>)
        1    0.000    0.000    0.000    0.000 protocols.py:162(DatagramProtocol)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceConnectionCreateEndParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 _local.py:851(WindowsPath)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceRequestChunkSentParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceRequestEndParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceDnsCacheHitParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 locks.py:12(_ContextManagerMixin)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceConnectionReuseconnParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 request.py:1215(ProxyDigestAuthHandler)
        1    0.000    0.000    0.000    0.000 message.py:1204(EmailMessage)
        1    0.000    0.000    0.000    0.000 response.py:37(addclosehook)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._OrValidator>:1(<module>)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._MaxLengthValidator>:1(<module>)
        1    0.000    0.000    0.000    0.000 base.py:179(__init__)
        1    0.000    0.000    0.000    0.000 tracing.py:273(TraceConnectionQueuedStartParams)
        1    0.000    0.000    0.000    0.000 core.py:20(IDNABidiError)
        1    0.000    0.000    0.000    0.000 idna.py:272(IncrementalEncoder)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceConnectionQueuedStartParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 streams.py:556(__init__)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr._make._AndValidator>:1(<module>)
        1    0.000    0.000    0.000    0.000 parse.py:144(_ResultMixinBytes)
        1    0.000    0.000    0.000    0.000 {method '__exit__' of 'posix.ScandirIterator' objects}
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.client_reqrep.ContentDisposition>:1(<module>)
        1    0.000    0.000    0.000    0.000 models.py:83(WSHandshakeError)
        1    0.000    0.000    0.000    0.000 error.py:19(URLError)
        1    0.000    0.000    0.000    0.000 http_exceptions.py:61(PayloadEncodingError)
        1    0.000    0.000    0.000    0.000 threading.py:1397(_DummyThread)
        1    0.000    0.000    0.000    0.000 typing.py:1762(copy_with)
        1    0.000    0.000    0.000    0.000 _base.py:45(Error)
        3    0.000    0.000    0.000    0.000 dataclasses.py:351(__init__)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceConnectionCreateStartParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 <attrs generated methods aiohttp.tracing.TraceRequestHeadersSentParams>:1(<module>)
        1    0.000    0.000    0.000    0.000 response.py:57(addinfo)
        1    0.000    0.000    0.000    0.000 core.py:14(IDNAError)
        1    0.000    0.000    0.000    0.000 calendar.py:39(IllegalWeekdayError)
        1    0.000    0.000    0.000    0.000 errors.py:85(HeaderDefect)
        1    0.000    0.000    0.000    0.000 argparse.py:235(_add_item)
        1    0.000    0.000    0.000    0.000 argparse.py:660(ArgumentDefaultsHelpFormatter)
        2    0.000    0.000    0.000    0.000 unix_events.py:605(__del__)
        1    0.000    0.000    0.000    0.000 lofi_cli.py:36(__init__)
        1    0.000    0.000    0.000    0.000 request.py:869(HTTPPasswordMgrWithDefaultRealm)
        2    0.000    0.000    0.000    0.000 {method 'detach' of '_socket.socket' objects}
        1    0.000    0.000    0.000    0.000 exceptions.py:47(LimitOverrunError)
        1    0.000    0.000    0.000    0.000 protocols.py:177(SubprocessProtocol)
        3    0.000    0.000    0.000    0.000 contextlib.py:568(__enter__)
        1    0.000    0.000    0.000    0.000 _policybase.py:41(__init__)
        2    0.000    0.000    0.000    0.000 argparse.py:1340(__init__)
        1    0.000    0.000    0.000    0.000 errors.py:91(InvalidHeaderDefect)
        1    0.000    0.000    0.000    0.000 request.py:1032(ProxyBasicAuthHandler)
        1    0.000    0.000    0.000    0.000 protocols.py:109(BufferedProtocol)
        1    0.000    0.000    0.000    0.000 parser.py:122(BytesHeaderParser)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._NotValidator>:1(<module>)
        1    0.000    0.000    0.000    0.000 temptypes.py:58(AsyncTemporaryDirectory)
        2    0.000    0.000    0.000    0.000 unix_events.py:1410(__exit__)
        1    0.000    0.000    0.000    0.000 argparse.py:1159(_ChoicesPseudoAction)
        1    0.000    0.000    0.000    0.000 protocols.py:66(Protocol)
        1    0.000    0.000    0.000    0.000 queue.py:31(Full)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._NumberValidator>:1(<module>)
        1    0.000    0.000    0.000    0.000 ipaddress.py:20(AddressValueError)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:273(ConnectionTimeoutError)
        3    0.000    0.000    0.000    0.000 freesound.py:30(source_name)
        1    0.000    0.000    0.000    0.000 argparse.py:913(_StoreAction)
        1    0.000    0.000    0.000    0.000 events.py:681(_Local)
        1    0.000    0.000    0.000    0.000 {built-in method posix.WIFSTOPPED}
        1    0.000    0.000    0.000    0.000 connector.py:814(__contains__)
        1    0.000    0.000    0.000    0.000 core.py:26(InvalidCodepoint)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._IsCallableValidator>:1(<module>)
        1    0.000    0.000    0.000    0.000 ipaddress.py:1131(_BaseConstants)
        4    0.000    0.000    0.000    0.000 enum.py:180(__init__)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:476(connection)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._InValidator>:1(<module>)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._SubclassOfValidator>:1(<module>)
        1    0.000    0.000    0.000    0.000 errors.py:20(BoundaryError)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._OptionalValidator>:1(<module>)
        1    0.000    0.000    0.000    0.000 shutil.py:70(SameFileError)
        1    0.000    0.000    0.000    0.000 argparse.py:1333(Namespace)
        1    0.000    0.000    0.000    0.000 {built-in method sys.gettrace}
        1    0.000    0.000    0.000    0.000 streams.py:36(EofStream)
        5    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._IsCallableValidator>:9(__init__)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:442(__del__)
        2    0.000    0.000    0.000    0.000 cookies.py:465(__init__)
        3    0.000    0.000    0.000    0.000 typing.py:2371(cast)
        1    0.000    0.000    0.000    0.000 parse.py:350(SplitResultBytes)
        1    0.000    0.000    0.000    0.000 argparse.py:203(_Section)
        1    0.000    0.000    0.000    0.000 events.py:84(cancelled)
        2    0.000    0.000    0.000    0.000 protocols.py:100(eof_received)
        1    0.000    0.000    0.000    0.000 transports.py:172(DatagramTransport)
        2    0.000    0.000    0.000    0.000 client_reqrep.py:939(ssl)
        1    0.000    0.000    0.000    0.000 http_exceptions.py:55(HttpBadRequest)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:420(headers)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._InstanceOfValidator>:1(<module>)
        4    0.000    0.000    0.000    0.000 {built-in method _sre.ascii_iscased}
        1    0.000    0.000    0.000    0.000 connector.py:181(__bool__)
        1    0.000    0.000    0.000    0.000 http_exceptions.py:69(TransferEncodingError)
        2    0.000    0.000    0.000    0.000 __init__.py:1030(setFormatter)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1154(update_auth)
        1    0.000    0.000    0.000    0.000 parse.py:331(SplitResult)
        1    0.000    0.000    0.000    0.000 base_subprocess.py:132(__del__)
        2    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._OptionalValidator>:12(__init__)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:61(ClientError)
        1    0.000    0.000    0.000    0.000 tasks.py:1007(create_eager_task_factory)
        1    0.000    0.000    0.000    0.000 exceptions.py:72(PythonTooOldError)
        1    0.000    0.000    0.000    0.000 argparse.py:950(_StoreConstAction)
        1    0.000    0.000    0.000    0.000 argparse.py:638(RawDescriptionHelpFormatter)
        1    0.000    0.000    0.000    0.000 argparse.py:681(MetavarTypeHelpFormatter)
        1    0.000    0.000    0.000    0.000 errors.py:8(MessageError)
        1    0.000    0.000    0.000    0.000 client.py:1539(LineTooLong)
        1    0.000    0.000    0.000    0.000 argparse.py:1053(_AppendConstAction)
        1    0.000    0.000    0.000    0.000 {method 'clear' of 'set' objects}
        1    0.000    0.000    0.000    0.000 queues.py:18(QueueEmpty)
        1    0.000    0.000    0.000    0.000 _local.py:472(PureWindowsPath)
        1    0.000    0.000    0.000    0.000 threading.py:1368(_DeleteDummyThreadOnDel)
        1    0.000    0.000    0.000    0.000 warnings.py:207(_OptionError)
        1    0.000    0.000    0.000    0.000 {built-in method _thread._make_thread_handle}
        1    0.000    0.000    0.000    0.000 pickle.py:73(PickleError)
        1    0.000    0.000    0.000    0.000 tracing.py:283(TraceConnectionCreateStartParams)
        1    0.000    0.000    0.000    0.000 request.py:1617(DataHandler)
        1    0.000    0.000    0.000    0.000 argparse.py:1013(_AppendAction)
        1    0.000    0.000    0.000    0.000 traceback.py:94(_Sentinel)
        1    0.000    0.000    0.000    0.000 {built-in method math.sqrt}
        1    0.000    0.000    0.000    0.000 error.py:70(ContentTooShortError)
        1    0.000    0.000    0.000    0.000 base.py:111(MusicSourceError)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:161(TooManyRedirects)
        1    0.000    0.000    0.000    0.000 socket.py:212(_GiveupOnSendfile)
        1    0.000    0.000    0.000    0.000 parse.py:355(ParseResultBytes)
        1    0.000    0.000    0.000    0.000 <frozen codecs>:263(__init__)
        1    0.000    0.000    0.000    0.000 client.py:1544(RemoteDisconnected)
        1    0.000    0.000    0.000    0.000 {method 'group' of 're.Match' objects}
        1    0.000    0.000    0.000    0.000 tracing.py:288(TraceConnectionCreateEndParams)
        1    0.000    0.000    0.000    0.000 tracing.py:278(TraceConnectionQueuedEndParams)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:148(WSServerHandshakeError)
        1    0.000    0.000    0.000    0.000 _abc.py:48(UnsupportedOperation)
        1    0.000    0.000    0.000    0.000 exceptions.py:10(CancelledError)
        1    0.000    0.000    0.000    0.000 argparse.py:975(_StoreTrueAction)
        1    0.000    0.000    0.000    0.000 argparse.py:723(ArgumentError)
        1    0.000    0.000    0.000    0.000 tracing.py:293(TraceConnectionReuseconnParams)
        1    0.000    0.000    0.000    0.000 cookies.py:145(CookieError)
        1    0.000    0.000    0.000    0.000 parse.py:336(ParseResult)
        1    0.000    0.000    0.000    0.000 binary.py:5(AsyncBufferedIOBase)
        1    0.000    0.000    0.000    0.000 text.py:5(AsyncTextIOWrapper)
        1    0.000    0.000    0.000    0.000 argparse.py:649(RawTextHelpFormatter)
        1    0.000    0.000    0.000    0.000 {method 'compression' of '_ssl._SSLSocket' objects}
        1    0.000    0.000    0.000    0.000 pickle.py:97(_Stop)
        1    0.000    0.000    0.000    0.000 errors.py:51(CloseBoundaryNotFoundDefect)
        1    0.000    0.000    0.000    0.000 utils.py:31(cond_delegate_to_executor)
        1    0.000    0.000    0.000    0.000 subprocess.py:129(SubprocessError)
        1    0.000    0.000    0.000    0.000 client.py:1495(UnknownProtocol)
        1    0.000    0.000    0.000    0.000 client.py:1532(BadStatusLine)
        1    0.000    0.000    0.000    0.000 argparse.py:1082(_CountAction)
        1    0.000    0.000    0.000    0.000 compression_utils.py:75(__init__)
        1    0.000    0.000    0.000    0.000 _url.py:909(raw_fragment)
        1    0.000    0.000    0.000    0.000 connector.py:349(__del__)
        1    0.000    0.000    0.000    0.000 <attrs generated __repr__ attr._make.Attribute>:1(<module>)
        1    0.000    0.000    0.000    0.000 events.py:748(_RunningLoop)
        1    0.000    0.000    0.000    0.000 _base.py:651(BrokenExecutor)
        1    0.000    0.000    0.000    0.000 _parser.py:102(checklookbehindgroup)
        2    0.000    0.000    0.000    0.000 unix_events.py:1401(is_active)
        1    0.000    0.000    0.000    0.000 {built-in method _io.text_encoding}
        1    0.000    0.000    0.000    0.000 client_proto.py:70(upgraded)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:269(ServerTimeoutError)
        1    0.000    0.000    0.000    0.000 client.py:1503(UnimplementedFileMode)
        1    0.000    0.000    0.000    0.000 __init__.py:37(AttrsInstance)
        1    0.000    0.000    0.000    0.000 base_subprocess.py:304(ReadSubprocessPipeProto)
        1    0.000    0.000    0.000    0.000 argparse.py:1267(_ExtendAction)
        1    0.000    0.000    0.000    0.000 transports.py:148(Transport)
        1    0.000    0.000    0.000    0.000 {built-in method builtins.id}
        1    0.000    0.000    0.000    0.000 {built-in method atexit.register}
        1    0.000    0.000    0.000    0.000 multipart.py:76(BadContentDispositionParam)
        1    0.000    0.000    0.000    0.000 client.py:1484(HTTPException)
        1    0.000    0.000    0.000    0.000 streams.py:180(exception)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:144(ContentTypeError)
        1    0.000    0.000    0.000    0.000 <attrs generated __eq__ attr._make.Converter>:1(<module>)
        1    0.000    0.000    0.000    0.000 queues.py:23(QueueFull)
        1    0.000    0.000    0.000    0.000 idna.py:316(IncrementalDecoder)
        1    0.000    0.000    0.000    0.000 _base.py:49(CancelledError)
        1    0.000    0.000    0.000    0.000 {method 'removeprefix' of 'str' objects}
        1    0.000    0.000    0.000    0.000 connector.py:127(__del__)
        1    0.000    0.000    0.000    0.000 client.py:1529(ResponseNotReady)
        1    0.000    0.000    0.000    0.000 http_exceptions.py:111(InvalidURLError)
        1    0.000    0.000    0.000    0.000 warnings.py:212(_processoptions)
        1    0.000    0.000    0.000    0.000 streams.py:234(is_eof)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._InstanceOfValidator>:12(__init__)
        1    0.000    0.000    0.000    0.000 ipaddress.py:24(NetmaskValueError)
        1    0.000    0.000    0.000    0.000 shutil.py:73(SpecialFileError)
        1    0.000    0.000    0.000    0.000 argparse.py:994(_StoreFalseAction)
        2    0.000    0.000    0.000    0.000 calendar.py:101(__init__)
        1    0.000    0.000    0.000    0.000 exceptions.py:23(FrozenInstanceError)
        1    0.000    0.000    0.000    0.000 inspect.py:3106(return_annotation)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1117(update_content_encoding)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:215(ClientConnectorDNSError)
        1    0.000    0.000    0.000    0.000 errors.py:12(MessageParseError)
        1    0.000    0.000    0.000    0.000 <attrs generated __repr__ attr._make.Converter>:1(<module>)
        1    0.000    0.000    0.000    0.000 base.py:13(_loop)
        1    0.000    0.000    0.000    0.000 binary.py:28(AsyncBufferedReader)
        1    0.000    0.000    0.000    0.000 exceptions.py:17(InvalidStateError)
        1    0.000    0.000    0.000    0.000 base_subprocess.py:140(get_returncode)
        1    0.000    0.000    0.000    0.000 base_subprocess.py:244(_wait)
        1    0.000    0.000    0.000    0.000 <frozen codecs>:189(__init__)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:969(port)
        1    0.000    0.000    0.000    0.000 core.py:32(InvalidCodepointContext)
        1    0.000    0.000    0.000    0.000 http_exceptions.py:65(ContentEncodingError)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:165(ClientConnectionError)
        2    0.000    0.000    0.000    0.000 subprocess.py:168(_noop)
        1    0.000    0.000    0.000    0.000 threading.py:1350(_MainThread)
        1    0.000    0.000    0.000    0.000 calendar.py:206(setfirstweekday)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:297(ClientPayloadError)
        1    0.000    0.000    0.000    0.000 <attrs generated __repr__ attr._make._CountingAttr>:1(<module>)
        1    0.000    0.000    0.000    0.000 <string>:1(<module>)
        1    0.000    0.000    0.000    0.000 parser.py:58(Error)
        1    0.000    0.000    0.000    0.000 thread.py:116(BrokenThreadPool)
        1    0.000    0.000    0.000    0.000 errors.py:94(HeaderMissingRequiredValue)
        1    0.000    0.000    0.000    0.000 subprocess.py:1102(__enter__)
        1    0.000    0.000    0.000    0.000 base.py:123(AuthenticationError)
        1    0.000    0.000    0.000    0.000 threading.py:837(BrokenBarrierError)
        1    0.000    0.000    0.000    0.000 queue.py:36(ShutDown)
        1    0.000    0.000    0.000    0.000 <attrs generated __hash__ attr._make.Attribute>:1(<module>)
        1    0.000    0.000    0.000    0.000 argparse.py:743(ArgumentTypeError)
        1    0.000    0.000    0.000    0.000 client.py:1412(trust_env)
        1    0.000    0.000    0.000    0.000 errors.py:108(ObsoleteHeaderDefect)
        1    0.000    0.000    0.000    0.000 binary.py:79(AsyncIndirectBufferedReader)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:277(SocketTimeoutError)
        1    0.000    0.000    0.000    0.000 idna.py:370(StreamWriter)
        1    0.000    0.000    0.000    0.000 errors.py:24(MultipartConversionError)
        1    0.000    0.000    0.000    0.000 client.py:1500(UnknownTransferEncoding)
        1    0.000    0.000    0.000    0.000 <attrs generated __repr__ attr._make.Factory>:1(<module>)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:106(_gen_default_accept_encoding)
        1    0.000    0.000    0.000    0.000 pickle.py:77(PicklingError)
        1    0.000    0.000    0.000    0.000 resolver.py:81(close)
        1    0.000    0.000    0.000    0.000 client.py:1489(NotConnected)
        1    0.000    0.000    0.000    0.000 http_exceptions.py:73(ContentLengthError)
        1    0.000    0.000    0.000    0.000 binary.py:33(AsyncFileIO)
        1    0.000    0.000    0.000    0.000 queues.py:28(QueueShutDown)
        1    0.000    0.000    0.000    0.000 {built-in method sys.getrecursionlimit}
        1    0.000    0.000    0.000    0.000 errors.py:16(HeaderParseError)
        1    0.000    0.000    0.000    0.000 errors.py:45(NoBoundaryInMultipartDefect)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:169(ClientConnectionResetError)
        1    0.000    0.000    0.000    0.000 text.py:36(AsyncTextIndirectIOWrapper)
        1    0.000    0.000    0.000    0.000 _url.py:653(scheme)
        1    0.000    0.000    0.000    0.000 setters.py:11(pipe)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:223(ClientProxyConnectionError)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:420(WSMessageTypeError)
        1    0.000    0.000    0.000    0.000 client.py:1492(InvalidURL)
        1    0.000    0.000    0.000    0.000 <attrs generated __eq__ attr._make.Attribute>:1(<module>)
        1    0.000    0.000    0.000    0.000 client_reqrep.py:1093(update_cookies)
        1    0.000    0.000    0.000    0.000 pickle.py:84(UnpicklingError)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:173(ClientOSError)
        1    0.000    0.000    0.000    0.000 errors.py:28(CharsetError)
        1    0.000    0.000    0.000    0.000 exceptions.py:31(FrozenAttributeError)
        1    0.000    0.000    0.000    0.000 exceptions.py:21(SendfileNotAvailableError)
        2    0.000    0.000    0.000    0.000 unix_events.py:1407(__enter__)
        2    0.000    0.000    0.000    0.000 contextlib.py:447(__enter__)
        1    0.000    0.000    0.000    0.000 idna.py:373(StreamReader)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:254(ServerConnectionError)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:338(InvalidUrlClientError)
        1    0.000    0.000    0.000    0.000 errors.py:54(FirstHeaderLineIsContinuationDefect)
        1    0.000    0.000    0.000    0.000 errors.py:65(MultipartInvariantViolationDefect)
        1    0.000    0.000    0.000    0.000 errors.py:111(NonASCIILocalPartDefect)
        1    0.000    0.000    0.000    0.000 client.py:1520(ImproperConnectionState)
        1    0.000    0.000    0.000    0.000 exceptions.py:47(NotAnAttrsClassError)
        1    0.000    0.000    0.000    0.000 binary.py:84(AsyncIndirectFileIO)
        1    0.000    0.000    0.000    0.000 exceptions.py:61(BrokenBarrierError)
        1    0.000    0.000    0.000    0.000 <attrs generated __hash__ attr._make.Factory>:1(<module>)
        1    0.000    0.000    0.000    0.000 {built-in method time.time}
        1    0.000    0.000    0.000    0.000 errors.py:116(InvalidDateDefect)
        1    0.000    0.000    0.000    0.000 client.py:1523(CannotSendRequest)
        1    0.000    0.000    0.000    0.000 exceptions.py:64(UnannotatedAttributeError)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:382(ClientConnectorSSLError)
        1    0.000    0.000    0.000    0.000 errors.py:57(MisplacedEnvelopeHeaderDefect)
        1    0.000    0.000    0.000    0.000 exceptions.py:55(DefaultAlreadySetError)
        1    0.000    0.000    0.000    0.000 shutil.py:87(_GiveupOnFastCopy)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:152(ClientHttpProxyError)
        1    0.000    0.000    0.000    0.000 <attrs generated __eq__ attr._make.Factory>:1(<module>)
        1    0.000    0.000    0.000    0.000 _base.py:55(InvalidStateError)
        1    0.000    0.000    0.000    0.000 errors.py:60(MissingHeaderBodySeparatorDefect)
        1    0.000    0.000    0.000    0.000 errors.py:68(InvalidMultipartContentTransferEncodingDefect)
        1    0.000    0.000    0.000    0.000 errors.py:71(UndecodableBytesDefect)
        1    0.000    0.000    0.000    0.000 errors.py:77(InvalidBase64CharactersDefect)
        1    0.000    0.000    0.000    0.000 client.py:1526(CannotSendHeader)
        1    0.000    0.000    0.000    0.000 package_data.py:1(<module>)
        1    0.000    0.000    0.000    0.000 shutil.py:77(ExecError)
        1    0.000    0.000    0.000    0.000 shutil.py:80(ReadError)
        1    0.000    0.000    0.000    0.000 shutil.py:83(RegistryError)
        1    0.000    0.000    0.000    0.000 utils.py:10(addr_to_addr_infos)
        1    0.000    0.000    0.000    0.000 errors.py:32(HeaderWriteError)
        1    0.000    0.000    0.000    0.000 errors.py:74(InvalidBase64PaddingDefect)
        1    0.000    0.000    0.000    0.000 errors.py:80(InvalidBase64LengthDefect)
        1    0.000    0.000    0.000    0.000 exceptions.py:39(AttrsAttributeNotFoundError)
        1    0.000    0.000    0.000    0.000 <attrs generated methods attr.validators._SubclassOfValidator>:12(__init__)
        1    0.000    0.000    0.000    0.000 binary.py:56(AsyncIndirectBufferedIOBase)
        1    0.000    0.000    0.000    0.000 {method 'uncancel' of '_asyncio.Task' objects}
        1    0.000    0.000    0.000    0.000 client_exceptions.py:342(RedirectClientError)
        1    0.000    0.000    0.000    0.000 errors.py:48(StartBoundaryNotFoundDefect)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:346(NonHttpUrlClientError)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:358(ClientSSLError)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:354(NonHttpUrlRedirectClientError)
        1    0.000    0.000    0.000    0.000 <attrs generated __eq__ attr._make._CountingAttr>:1(<module>)
        1    0.000    0.000    0.000    0.000 client_exceptions.py:350(InvalidUrlRedirectClientError)
        1    0.000    0.000    0.000    0.000 selectors.py:203(__enter__)
        1    0.000    0.000    0.000    0.000 <attrs generated __hash__ attr._make.Converter>:1(<module>)
        1    0.000    0.000    0.000    0.000 {method '_make_cancelled_error' of '_asyncio.Task' objects}
        1    0.000    0.000    0.000    0.000 runners.py:155(<lambda>)
        1    0.000    0.000    0.000    0.000 <frozen codecs>:217(setstate)
        1    0.000    0.000    0.000    0.000 {built-in method _thread._get_main_thread_ident}
        1    0.000    0.000    0.000    0.000 unix_events.py:1436(attach_loop)
        1    0.000    0.000    0.000    0.000 inspect.py:3102(parameters)
        1    0.000    0.000    0.000    0.000 inspect.py:2807(name)
        1    0.000    0.000    0.000    0.000 <frozen importlib._bootstrap_external>:1439(exec_module)


