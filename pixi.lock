version: 6
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ffmpeg-7.1.1-gpl_h127656b_906.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.12-hb9ae30d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.2.1-h3beb420_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/level-zero-1.23.0-h84d6215_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libass-0.17.3-h52826cd_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.75-h39aace5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-lib-1.11.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.55-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-batch-plugin-2025.0.0-h4d9b6c2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-plugin-2025.0.0-h4d9b6c2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-hetero-plugin-2025.0.0-h981d57b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-cpu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-gpu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-npu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-ir-frontend-2025.0.0-h981d57b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-onnx-frontend-2025.0.0-h0e684df_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-paddle-frontend-2025.0.0-h0e684df_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-pytorch-frontend-2025.0.0-h5888daf_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-frontend-2025.0.0-h684f15b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5888daf_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.58.4-he92a37e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-257.7-h4e0b6ca_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libudev1-257.7-hbe16f8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libunwind-1.6.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liburing-2.10-h84d6215_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libusb-1.0.29-h73b1eb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libva-2.22.0-h4f16b4b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvpx-1.14.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/opencl-headers-2025.06.13-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openh264-2.6.0-hc22cd8d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pango-1.56.3-h9ac818e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.2-h29eaf8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pugixml-1.15-h3f63f65_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hac146a9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2-2.32.54-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl3-3.2.16-he3e324a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2022.1.0-h4ce085d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wayland-protocols-1.45-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x264-1!164.3095-h166bdaf_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxscrnsaver-1.2.4-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      - pypi: https://files.pythonhosted.org/packages/a5/45/30bb92d442636f570cb5651bc661f52b610e2eec3f891a5dc3a4c3667db0/aiofiles-24.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0f/15/5bf3b99495fb160b63f95972b81750f18f7f4e02ad051373b669d17d44f2/aiohappyeyeballs-2.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/48/19/0377df97dd0176ad23cd8cad4fd4232cfeadcec6c1b7f036315305c98e3f/aiohttp-3.12.13-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/ec/6a/bc7e17a3e87a2985d3e8f4da4cd0f481060eb78fb08596c42be62c90a4d9/aiosignal-1.3.2-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/77/06/bb80f5f86020c4551da315d78b3ab75e8228f89f0162f2c3a819e407941a/attrs-25.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/84/ae/320161bd181fc06471eed047ecce67b693fd7515b16d495d8932db763426/certifi-2025.6.15-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/72/31/bc8c5c99c7818293458fe745dab4fd5730ff49697ccc82b554eb69f16a24/frozenlist-1.7.0-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/69/cb/b84afdb961dcf09b8e8c0238f068122d85480bfaac2c5c0b03120e497318/multidict-6.6.2-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/af/81/b324c44ae60c56ef12007105f1460d5c304b0626ab0cc6b07c8f2a9aa0b8/propcache-0.3.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/5f/ed/539768cf28c661b5b068d66d96a2f155c4971a5d55684a514c1a0e0dec2f/python_dotenv-1.1.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/01/a1/fc4856bd02d2097324fb7ce05b3021fb850f864b83ca765f6e37e92ff8ca/sentry_sdk-2.32.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/af/44/46407d7f7a56e9a85a4c207724c9f2c545c060380718eea9088f222ba697/yarl-1.20.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  dev:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ffmpeg-7.1.1-gpl_h127656b_906.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.12-hb9ae30d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.2.1-h3beb420_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/level-zero-1.23.0-h84d6215_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libass-0.17.3-h52826cd_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.75-h39aace5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-lib-1.11.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.55-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-batch-plugin-2025.0.0-h4d9b6c2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-plugin-2025.0.0-h4d9b6c2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-hetero-plugin-2025.0.0-h981d57b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-cpu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-gpu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-npu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-ir-frontend-2025.0.0-h981d57b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-onnx-frontend-2025.0.0-h0e684df_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-paddle-frontend-2025.0.0-h0e684df_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-pytorch-frontend-2025.0.0-h5888daf_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-frontend-2025.0.0-h684f15b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5888daf_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.58.4-he92a37e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-257.7-h4e0b6ca_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libudev1-257.7-hbe16f8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libunwind-1.6.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liburing-2.10-h84d6215_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libusb-1.0.29-h73b1eb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libva-2.22.0-h4f16b4b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvpx-1.14.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/opencl-headers-2025.06.13-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openh264-2.6.0-hc22cd8d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pango-1.56.3-h9ac818e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.2-h29eaf8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pugixml-1.15-h3f63f65_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hac146a9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2-2.32.54-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl3-3.2.16-he3e324a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2022.1.0-h4ce085d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wayland-protocols-1.45-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x264-1!164.3095-h166bdaf_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxscrnsaver-1.2.4-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      - pypi: https://files.pythonhosted.org/packages/a5/45/30bb92d442636f570cb5651bc661f52b610e2eec3f891a5dc3a4c3667db0/aiofiles-24.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0f/15/5bf3b99495fb160b63f95972b81750f18f7f4e02ad051373b669d17d44f2/aiohappyeyeballs-2.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/48/19/0377df97dd0176ad23cd8cad4fd4232cfeadcec6c1b7f036315305c98e3f/aiohttp-3.12.13-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/ec/6a/bc7e17a3e87a2985d3e8f4da4cd0f481060eb78fb08596c42be62c90a4d9/aiosignal-1.3.2-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/77/06/bb80f5f86020c4551da315d78b3ab75e8228f89f0162f2c3a819e407941a/attrs-25.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e3/ee/adda3d46d4a9120772fae6de454c8495603c37c4c3b9c60f25b1ab6401fe/black-25.1.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/84/ae/320161bd181fc06471eed047ecce67b693fd7515b16d495d8932db763426/certifi-2025.6.15-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c5/55/51844dd50c4fc7a33b653bfaba4c2456f06955289ca770a5dbd5fd267374/cfgv-3.4.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/91/a1/cf2472db20f7ce4a6be1253a81cfdf85ad9c7885ffbed7047fb72c24cf87/distlib-0.3.9-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/4d/36/2a115987e2d8c300a974597416d9de88f2444426de9571f4b59b2cca3acc/filelock-3.18.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9f/56/13ab06b4f93ca7cac71078fbe37fcea175d3216f31f85c3168a6bbd0bb9a/flake8-7.3.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/72/31/bc8c5c99c7818293458fe745dab4fd5730ff49697ccc82b554eb69f16a24/frozenlist-1.7.0-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/7a/cd/18f8da995b658420625f7ef13f037be53ae04ec5ad33f9b718240dcfd48c/identify-2.6.12-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2c/e1/e6716421ea10d38022b952c159d5161ca1193197fb744506875fbb87ea7b/iniconfig-2.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/27/1a/1f68f9ba0c207934b35b86a8ca3aad8395a3d6dd7921c0686e23853ff5a9/mccabe-0.7.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/69/cb/b84afdb961dcf09b8e8c0238f068122d85480bfaac2c5c0b03120e497318/multidict-6.6.2-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/b4/7e/81ca3b074021ad9775e5cb97ebe0089c0f13684b066a750b7dc208438403/mypy-1.16.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/79/7b/2c79738432f5c924bef5071f933bcc9efd0473bac3b4aa584a6f7c1c8df8/mypy_extensions-1.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d2/1d/1b658dbd2b9fa9c4c9f32accbfc0205d532c8c6194dc0f2a4c0428e7128a/nodeenv-1.9.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/cc/20/ff623b09d963f88bfde16306a54e12ee5ea43e9b597108672ff3a408aad6/pathspec-0.12.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fe/39/979e8e21520d4e47a0bbe349e2713c0aac6f3d853d0e5b34d76206c439aa/platformdirs-4.3.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/54/20/4d324d65cc6d9205fabedc306948156824eb9f0ee1633355a8f7ec5c66bf/pluggy-1.6.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/88/74/a88bf1b1efeae488a0c0b7bdf71429c313722d1fc0f377537fbe554e6180/pre_commit-4.2.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/af/81/b324c44ae60c56ef12007105f1460d5c304b0626ab0cc6b07c8f2a9aa0b8/propcache-0.3.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d7/27/a58ddaf8c588a3ef080db9d0b7e0b97215cee3a45df74f3a94dbbf5c893a/pycodestyle-2.14.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c2/2f/81d580a0fb83baeb066698975cb14a618bdbed7720678566f1b046a95fe8/pyflakes-3.4.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c7/21/705964c7812476f378728bdf590ca4b771ec72385c533964653c68e86bdc/pygments-2.19.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/29/16/c8a903f4c4dffe7a12843191437d7cd8e32751d5de349d45d3fe69544e87/pytest-8.4.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/30/05/ce271016e351fddc8399e546f6e23761967ee09c8c568bbfbecb0c150171/pytest_asyncio-1.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5f/ed/539768cf28c661b5b068d66d96a2f155c4971a5d55684a514c1a0e0dec2f/python_dotenv-1.1.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/04/24/b7721e4845c2f162d26f50521b825fb061bc0a5afcf9a386840f23ea19fa/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/01/a1/fc4856bd02d2097324fb7ce05b3021fb850f864b83ca765f6e37e92ff8ca/sentry_sdk-2.32.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/69/e0/552843e0d356fbb5256d21449fa957fa4eff3bbc135a74a691ee70c7c5da/typing_extensions-4.14.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f3/40/b1c265d4b2b62b58576588510fc4d1fe60a86319c8de99fd8e9fec617d2c/virtualenv-20.31.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/af/44/46407d7f7a56e9a85a4c207724c9f2c545c060380718eea9088f222ba697/yarl-1.20.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  purls: []
  size: 2562
  timestamp: 1578324546067
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 23621
  timestamp: 1650670423406
- pypi: https://files.pythonhosted.org/packages/a5/45/30bb92d442636f570cb5651bc661f52b610e2eec3f891a5dc3a4c3667db0/aiofiles-24.1.0-py3-none-any.whl
  name: aiofiles
  version: 24.1.0
  sha256: b4ec55f4195e3eb5d7abd1bf7e061763e864dd4954231fb8539a0ef8bb8260e5
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/0f/15/5bf3b99495fb160b63f95972b81750f18f7f4e02ad051373b669d17d44f2/aiohappyeyeballs-2.6.1-py3-none-any.whl
  name: aiohappyeyeballs
  version: 2.6.1
  sha256: f349ba8f4b75cb25c99c5c2d84e997e485204d2902a9597802b0371f09331fb8
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/48/19/0377df97dd0176ad23cd8cad4fd4232cfeadcec6c1b7f036315305c98e3f/aiohttp-3.12.13-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: aiohttp
  version: 3.12.13
  sha256: 7a0b9170d5d800126b5bc89d3053a2363406d6e327afb6afaeda2d19ee8bb103
  requires_dist:
  - aiohappyeyeballs>=2.5.0
  - aiosignal>=1.1.2
  - async-timeout>=4.0,<6.0 ; python_full_version < '3.11'
  - attrs>=17.3.0
  - frozenlist>=1.1.1
  - multidict>=4.5,<7.0
  - propcache>=0.2.0
  - yarl>=1.17.0,<2.0
  - aiodns>=3.3.0 ; extra == 'speedups'
  - brotli ; platform_python_implementation == 'CPython' and extra == 'speedups'
  - brotlicffi ; platform_python_implementation != 'CPython' and extra == 'speedups'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/ec/6a/bc7e17a3e87a2985d3e8f4da4cd0f481060eb78fb08596c42be62c90a4d9/aiosignal-1.3.2-py2.py3-none-any.whl
  name: aiosignal
  version: 1.3.2
  sha256: 45cde58e409a301715980c2b01d0c28bdde3770d8290b5eb2173759d9acb31a5
  requires_dist:
  - frozenlist>=1.1.0
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
  sha256: b9214bc17e89bf2b691fad50d952b7f029f6148f4ac4fe7c60c08f093efdf745
  md5: 76df83c2a9035c54df5d04ff81bcc02d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: GPL
  purls: []
  size: 566531
  timestamp: 1744668655747
- conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
  sha256: b08ef033817b5f9f76ce62dfcac7694e7b6b4006420372de22494503decac855
  md5: 346722a0be40f6edc53f12640d301338
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 2706396
  timestamp: 1718551242397
- conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
  sha256: 82c13b1772c21fc4a17441734de471d3aabf82b61db9b11f4a1bd04a9c4ac324
  md5: d9c69a24ad678ffce24c6543a0176b00
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 71042
  timestamp: 1660065501192
- pypi: https://files.pythonhosted.org/packages/77/06/bb80f5f86020c4551da315d78b3ab75e8228f89f0162f2c3a819e407941a/attrs-25.3.0-py3-none-any.whl
  name: attrs
  version: 25.3.0
  sha256: 427318ce031701fea540783410126f03899a97ffc6f61596ad581ac2e40e3bc3
  requires_dist:
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'benchmark'
  - hypothesis ; extra == 'benchmark'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'benchmark'
  - pympler ; extra == 'benchmark'
  - pytest-codspeed ; extra == 'benchmark'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'benchmark'
  - pytest-xdist[psutil] ; extra == 'benchmark'
  - pytest>=4.3.0 ; extra == 'benchmark'
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'cov'
  - coverage[toml]>=5.3 ; extra == 'cov'
  - hypothesis ; extra == 'cov'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'cov'
  - pympler ; extra == 'cov'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'cov'
  - pytest-xdist[psutil] ; extra == 'cov'
  - pytest>=4.3.0 ; extra == 'cov'
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'dev'
  - hypothesis ; extra == 'dev'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'dev'
  - pre-commit-uv ; extra == 'dev'
  - pympler ; extra == 'dev'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'dev'
  - pytest-xdist[psutil] ; extra == 'dev'
  - pytest>=4.3.0 ; extra == 'dev'
  - cogapp ; extra == 'docs'
  - furo ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - sphinx-notfound-page ; extra == 'docs'
  - sphinxcontrib-towncrier ; extra == 'docs'
  - towncrier ; extra == 'docs'
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'tests'
  - hypothesis ; extra == 'tests'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests'
  - pympler ; extra == 'tests'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests'
  - pytest-xdist[psutil] ; extra == 'tests'
  - pytest>=4.3.0 ; extra == 'tests'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests-mypy'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests-mypy'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/e3/ee/adda3d46d4a9120772fae6de454c8495603c37c4c3b9c60f25b1ab6401fe/black-25.1.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl
  name: black
  version: 25.1.0
  sha256: 030b9759066a4ee5e5aca28c3c77f9c64789cdd4de8ac1df642c40b708be6171
  requires_dist:
  - click>=8.0.0
  - mypy-extensions>=0.4.3
  - packaging>=22.0
  - pathspec>=0.9.0
  - platformdirs>=2
  - tomli>=1.1.0 ; python_full_version < '3.11'
  - typing-extensions>=4.0.1 ; python_full_version < '3.11'
  - colorama>=0.4.3 ; extra == 'colorama'
  - aiohttp>=3.10 ; extra == 'd'
  - ipython>=7.8.0 ; extra == 'jupyter'
  - tokenize-rt>=3.2.0 ; extra == 'jupyter'
  - uvloop>=0.15.2 ; extra == 'uvloop'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
  sha256: 7cfec9804c84844ea544d98bda1d9121672b66ff7149141b8415ca42dfcd44f6
  md5: 72525f07d72806e3b639ad4504c30ce5
  depends:
  - __unix
  license: ISC
  purls: []
  size: 151069
  timestamp: 1749990087500
- conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
  sha256: 3bd6a391ad60e471de76c0e9db34986c4b5058587fbf2efa5a7f54645e28c2c7
  md5: 09262e66b19567aff4f592fb53b28760
  depends:
  - __glibc >=2.17,<3.0.a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.5,<2.0a0
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  license: LGPL-2.1-only or MPL-1.1
  purls: []
  size: 978114
  timestamp: 1741554591855
- pypi: https://files.pythonhosted.org/packages/84/ae/320161bd181fc06471eed047ecce67b693fd7515b16d495d8932db763426/certifi-2025.6.15-py3-none-any.whl
  name: certifi
  version: 2025.6.15
  sha256: 2e0c7ce7cb5d8f8634ca55d2ba7e6ec2689a2fd6537d8dec1296a477a4910057
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/c5/55/51844dd50c4fc7a33b653bfaba4c2456f06955289ca770a5dbd5fd267374/cfgv-3.4.0-py2.py3-none-any.whl
  name: cfgv
  version: 3.4.0
  sha256: b7265b1f29fd3316bfcd2b330d63d024f2bfd8bcb8b0272f8e19a504856c48f9
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl
  name: click
  version: 8.2.1
  sha256: 61a3265b914e850b85317d0b3109c7f8cd35a670f963866005d6ef1d5175a12b
  requires_dist:
  - colorama ; sys_platform == 'win32'
  requires_python: '>=3.10'
- conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
  sha256: 22053a5842ca8ee1cf8e1a817138cdb5e647eb2c46979f84153f6ad7bde73020
  md5: 418c6ca5929a611cbd69204907a83995
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 760229
  timestamp: 1685695754230
- conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
  sha256: 3b988146a50e165f0fa4e839545c679af88e4782ec284cc7b6d07dd226d6a068
  md5: 679616eb5ad4e521c83da4650860aba7
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libexpat >=2.7.0,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - libglib >=2.84.2,<3.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 437860
  timestamp: 1747855126005
- pypi: https://files.pythonhosted.org/packages/91/a1/cf2472db20f7ce4a6be1253a81cfdf85ad9c7885ffbed7047fb72c24cf87/distlib-0.3.9-py2.py3-none-any.whl
  name: distlib
  version: 0.3.9
  sha256: 47f8c22fd27c27e25a65601af709b38e4f0a45ea4fc2e710f65755fa8caaaf87
- conda: https://conda.anaconda.org/conda-forge/linux-64/ffmpeg-7.1.1-gpl_h127656b_906.conda
  sha256: e8e93a1afd93bed11ccf2a2224d2b92b2af8758c89576ed87ff4df7f3269604f
  md5: 28cffcba871461840275632bc4653ce3
  depends:
  - __glibc >=2.17,<3.0.a0
  - alsa-lib >=1.2.14,<1.3.0a0
  - aom >=3.9.1,<3.10.0a0
  - bzip2 >=1.0.8,<2.0a0
  - dav1d >=1.2.1,<1.2.2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - gmp >=6.3.0,<7.0a0
  - harfbuzz >=11.0.1
  - lame >=3.100,<3.101.0a0
  - libass >=0.17.3,<0.17.4.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libopenvino >=2025.0.0,<2025.0.1.0a0
  - libopenvino-auto-batch-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-auto-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-hetero-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-intel-cpu-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-intel-gpu-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-intel-npu-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-ir-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-onnx-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-paddle-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-pytorch-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-tensorflow-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-tensorflow-lite-frontend >=2025.0.0,<2025.0.1.0a0
  - libopus >=1.5.2,<2.0a0
  - librsvg >=2.58.4,<3.0a0
  - libstdcxx >=13
  - libva >=2.22.0,<3.0a0
  - libvorbis >=1.3.7,<1.4.0a0
  - libvpx >=1.14.1,<1.15.0a0
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - openh264 >=2.6.0,<2.6.1.0a0
  - openssl >=3.5.0,<4.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - sdl2 >=2.32.54,<3.0a0
  - svt-av1 >=3.0.2,<3.0.3.0a0
  - x264 >=1!164.3095,<1!165
  - x265 >=3.5,<3.6.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  constrains:
  - __cuda  >=12.8
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 10377191
  timestamp: 1748704974937
- pypi: https://files.pythonhosted.org/packages/4d/36/2a115987e2d8c300a974597416d9de88f2444426de9571f4b59b2cca3acc/filelock-3.18.0-py3-none-any.whl
  name: filelock
  version: 3.18.0
  sha256: c401f4f8377c4464e6db25fff06205fd89bdd83b65eb0488ed1b160f780e21de
  requires_dist:
  - furo>=2024.8.6 ; extra == 'docs'
  - sphinx-autodoc-typehints>=3 ; extra == 'docs'
  - sphinx>=8.1.3 ; extra == 'docs'
  - covdefaults>=2.3 ; extra == 'testing'
  - coverage>=7.6.10 ; extra == 'testing'
  - diff-cover>=9.2.1 ; extra == 'testing'
  - pytest-asyncio>=0.25.2 ; extra == 'testing'
  - pytest-cov>=6 ; extra == 'testing'
  - pytest-mock>=3.14 ; extra == 'testing'
  - pytest-timeout>=2.3.1 ; extra == 'testing'
  - pytest>=8.3.4 ; extra == 'testing'
  - virtualenv>=20.28.1 ; extra == 'testing'
  - typing-extensions>=4.12.2 ; python_full_version < '3.11' and extra == 'typing'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/9f/56/13ab06b4f93ca7cac71078fbe37fcea175d3216f31f85c3168a6bbd0bb9a/flake8-7.3.0-py2.py3-none-any.whl
  name: flake8
  version: 7.3.0
  sha256: b9696257b9ce8beb888cdbe31cf885c90d31928fe202be0889a7cdafad32f01e
  requires_dist:
  - mccabe>=0.7.0,<0.8.0
  - pycodestyle>=2.14.0,<2.15.0
  - pyflakes>=3.4.0,<3.5.0
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
  sha256: 58d7f40d2940dd0a8aa28651239adbf5613254df0f75789919c4e6762054403b
  md5: 0c96522c6bdaed4b1566d11387caaf45
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 397370
  timestamp: 1566932522327
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
  sha256: c52a29fdac682c20d252facc50f01e7c2e7ceac52aa9817aaf0bb83f7559ec5c
  md5: 34893075a5c9e55cdafac56607368fc6
  license: OFL-1.1
  license_family: Other
  purls: []
  size: 96530
  timestamp: 1620479909603
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
  sha256: 00925c8c055a2275614b4d983e1df637245e19058d79fc7dd1a93b8d9fb4b139
  md5: 4d59c254e01d9cde7957100457e2d5fb
  license: OFL-1.1
  license_family: Other
  purls: []
  size: 700814
  timestamp: 1620479612257
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
  sha256: 2821ec1dc454bd8b9a31d0ed22a7ce22422c0aef163c59f49dfdf915d0f0ca14
  md5: 49023d73832ef61042f6a237cb2687e7
  license: LicenseRef-Ubuntu-Font-Licence-Version-1.0
  license_family: Other
  purls: []
  size: 1620504
  timestamp: 1727511233259
- conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
  sha256: 7093aa19d6df5ccb6ca50329ef8510c6acb6b0d8001191909397368b65b02113
  md5: 8f5b0b297b59e1ac160ad4beec99dbee
  depends:
  - __glibc >=2.17,<3.0.a0
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 265599
  timestamp: 1730283881107
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
  sha256: a997f2f1921bb9c9d76e6fa2f6b408b7fa549edd349a77639c9fe7a23ea93e61
  md5: fee5683a3f04bd15cbd8318b096a27ab
  depends:
  - fonts-conda-forge
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 3667
  timestamp: 1566974674465
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
  sha256: 53f23a3319466053818540bcdf2091f253cbdbab1e0e9ae7b9e509dcaa2a5e38
  md5: f766549260d6815b0c52253f1fb1bb29
  depends:
  - font-ttf-dejavu-sans-mono
  - font-ttf-inconsolata
  - font-ttf-source-code-pro
  - font-ttf-ubuntu
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 4102
  timestamp: 1566932280397
- conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
  sha256: 7ef7d477c43c12a5b4cddcf048a83277414512d1116aba62ebadfa7056a7d84f
  md5: 9ccd736d31e0c6e41f54e704e5312811
  depends:
  - libfreetype 2.13.3 ha770c72_1
  - libfreetype6 2.13.3 h48d6fc4_1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 172450
  timestamp: 1745369996765
- conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
  sha256: 5d7b6c0ee7743ba41399e9e05a58ccc1cfc903942e49ff6f677f6e423ea7a627
  md5: ac7bc6a654f8f41b352b38f4051135f8
  depends:
  - libgcc-ng >=7.5.0
  license: LGPL-2.1
  purls: []
  size: 114383
  timestamp: 1604416621168
- pypi: https://files.pythonhosted.org/packages/72/31/bc8c5c99c7818293458fe745dab4fd5730ff49697ccc82b554eb69f16a24/frozenlist-1.7.0-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: frozenlist
  version: 1.7.0
  sha256: 8bd7eb96a675f18aa5c553eb7ddc24a43c8c18f22e1f9925528128c052cdbe00
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.12-hb9ae30d_0.conda
  sha256: d5283b95a8d49dcd88d29b360d8b38694aaa905d968d156d72ab71d32b38facb
  md5: 201db6c2d9a3c5e46573ac4cb2e92f4f
  depends:
  - libgcc-ng >=12
  - libglib >=2.80.2,<3.0a0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libtiff >=4.6.0,<4.8.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 528149
  timestamp: 1715782983957
- conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
  sha256: 88db27c666e1f8515174bf622a3e2ad983c94d69e3a23925089e476b9b06ad00
  md5: c63e7590d4d6f4c85721040ed8b12888
  depends:
  - __glibc >=2.17,<3.0.a0
  - gettext-tools 0.24.1 h5888daf_0
  - libasprintf 0.24.1 h8e693c7_0
  - libasprintf-devel 0.24.1 h8e693c7_0
  - libgcc >=13
  - libgettextpo 0.24.1 h5888daf_0
  - libgettextpo-devel 0.24.1 h5888daf_0
  - libstdcxx >=13
  license: LGPL-2.1-or-later AND GPL-3.0-or-later
  purls: []
  size: 511988
  timestamp: 1746228987123
- conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
  sha256: 3ba33868630b903e3cda7a9176363cdf02710fb8f961efed5f8200c4d53fb4e3
  md5: d54305672f0361c2f3886750e7165b5f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 3129801
  timestamp: 1746228937647
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
  sha256: 309cf4f04fec0c31b6771a5809a1909b4b3154a2208f52351e1ada006f4c750c
  md5: c94a5994ef49749880a8139cf9afcbe1
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  purls: []
  size: 460055
  timestamp: 1718980856608
- conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
  sha256: cac69f3ff7756912bbed4c28363de94f545856b35033c0b86193366b95f5317d
  md5: 951ff8d9e5536896408e89d63230b8d5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.0-or-later
  license_family: LGPL
  purls: []
  size: 98419
  timestamp: 1750079957535
- conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.2.1-h3beb420_0.conda
  sha256: 5bd0f3674808862838d6e2efc0b3075e561c34309c5c2f4c976f7f1f57c91112
  md5: 0e6e192d4b3d95708ad192d957cf3163
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - freetype
  - graphite2
  - icu >=75.1,<76.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libglib >=2.84.1,<3.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 1730226
  timestamp: 1747091044218
- conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
  sha256: 71e750d509f5fa3421087ba88ef9a7b9be11c53174af3aa4d06aff4c18b38e8e
  md5: 8b189310083baabfb622af68fd9d3ae3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  purls: []
  size: 12129203
  timestamp: 1720853576813
- pypi: https://files.pythonhosted.org/packages/7a/cd/18f8da995b658420625f7ef13f037be53ae04ec5ad33f9b718240dcfd48c/identify-2.6.12-py2.py3-none-any.whl
  name: identify
  version: 2.6.12
  sha256: ad9672d5a72e0d2ff7c5c8809b62dfa60458626352fb0eb7b55e69bdc45334a2
  requires_dist:
  - ukkonen ; extra == 'license'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
  name: idna
  version: '3.10'
  sha256: 946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3
  requires_dist:
  - ruff>=0.6.2 ; extra == 'all'
  - mypy>=1.11.2 ; extra == 'all'
  - pytest>=8.3.2 ; extra == 'all'
  - flake8>=7.1.1 ; extra == 'all'
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/2c/e1/e6716421ea10d38022b952c159d5161ca1193197fb744506875fbb87ea7b/iniconfig-2.1.0-py3-none-any.whl
  name: iniconfig
  version: 2.1.0
  sha256: 9deba5723312380e77435581c6bf4935c94cbfab9b1ed33ef8d238ea168eb760
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
  sha256: aad2a703b9d7b038c0f745b853c6bb5f122988fe1a7a096e0e606d9cbec4eaab
  md5: a8832b479f93521a9e7b5b743803be51
  depends:
  - libgcc-ng >=12
  license: LGPL-2.0-only
  license_family: LGPL
  purls: []
  size: 508258
  timestamp: 1664996250081
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
  sha256: dcd2b1a065bbf5c54004ddf6551c775a8eb6993c8298ca8a6b92041ed413f785
  md5: 6dc9e1305e7d3129af4ad0dabda30e56
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.43
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 670635
  timestamp: 1749858327854
- conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
  sha256: 412381a43d5ff9bbed82cd52a0bbca5b90623f62e41007c9c42d3870c60945ff
  md5: 9344155d33912347b37f0ae6c410a835
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 264243
  timestamp: 1745264221534
- conda: https://conda.anaconda.org/conda-forge/linux-64/level-zero-1.23.0-h84d6215_0.conda
  sha256: 143e01d63c103baf6cd27372736c90c75d78814d24105adcfb34900abd2bcdd5
  md5: 917379a89f84d15d3e871909553c2320
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 598789
  timestamp: 1750920203501
- conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
  sha256: 65d5ca837c3ee67b9d769125c21dc857194d7f6181bb0e7bd98ae58597b457d0
  md5: 00290e549c5c8a32cc271020acc9ec6b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - abseil-cpp =20250127.1
  - libabseil-static =20250127.1=cxx17*
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 1325007
  timestamp: 1742369558286
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
  sha256: e30733a729eb6efd9cb316db0202897c882d46f6c20a0e647b4de8ec921b7218
  md5: 57566a81dd1e5aa3d98ac7582e8bfe03
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 53115
  timestamp: 1746228856865
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
  sha256: ccbfc465456133042eea3e8d69bae009893f57a47a786f772c0af382bda7ad99
  md5: 8f66ed2e34507b7ae44afa31c3e4ec79
  depends:
  - __glibc >=2.17,<3.0.a0
  - libasprintf 0.24.1 h8e693c7_0
  - libgcc >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 34680
  timestamp: 1746228884730
- conda: https://conda.anaconda.org/conda-forge/linux-64/libass-0.17.3-h52826cd_2.conda
  sha256: 8a94e634de73be1e7548deaf6e3b992e0d30c628a24f23333af06ebb3a3e74cb
  md5: 01de25a48490709850221135890e09eb
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libzlib >=1.3.1,<2.0a0
  - libiconv >=1.18,<2.0a0
  - fribidi >=1.0.10,<2.0a0
  - freetype >=2.13.3,<3.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - harfbuzz >=11.0.0,<12.0a0
  license: ISC
  purls: []
  size: 152563
  timestamp: 1743206970222
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.75-h39aace5_0.conda
  sha256: 9c84448305e7c9cc44ccec7757cf5afcb5a021f4579aa750a1fa6ea398783950
  md5: c44c16d6976d2aebbd65894d7741e67e
  depends:
  - __glibc >=2.17,<3.0.a0
  - attr >=2.5.1,<2.6.0a0
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 120375
  timestamp: 1741176638215
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
  sha256: 8420748ea1cc5f18ecc5068b4f24c7a023cc9b20971c99c824ba10641fb95ddf
  md5: 64f0c503da58ec25ebd359e4d990afa8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 72573
  timestamp: 1747040452262
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
  sha256: f53458db897b93b4a81a6dbfd7915ed8fa4a54951f97c698dde6faa028aadfd2
  md5: 4c0ab57463117fbb8df85268415082f5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpciaccess >=0.18,<0.19.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 246161
  timestamp: 1749904704373
- conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
  sha256: 7fd5408d359d05a969133e47af580183fbf38e2235b562193d427bb9dad79723
  md5: c151d5eb730e9b7480e6d48c0fc44048
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  purls: []
  size: 44840
  timestamp: 1731330973553
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
  sha256: 33ab03438aee65d6aa667cf7d90c91e5e7d734c19a67aa4c7040742c0a13d505
  md5: db0bfbe7dd197b68ad5f30333bae6ce0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  purls: []
  size: 74427
  timestamp: 1743431794976
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
  sha256: 65908b75fa7003167b8a8f0001e11e58ed5b1ef5e98b96ab2ba66d7c1b822c7d
  md5: ee48bf17cc83a00f59ca1494d5646869
  depends:
  - gettext >=0.21.1,<1.0a0
  - libgcc-ng >=12
  - libogg 1.3.*
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 394383
  timestamp: 1687765514062
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
  sha256: 7be9b3dac469fe3c6146ff24398b685804dfc7a1de37607b84abd076f57cc115
  md5: 51f5be229d83ecd401fb369ab96ae669
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  purls: []
  size: 7693
  timestamp: 1745369988361
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
  sha256: 7759bd5c31efe5fbc36a7a1f8ca5244c2eabdbeb8fc1bee4b99cf989f35c7d81
  md5: 3c255be50a506c50765a93a6644f32fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  purls: []
  size: 380134
  timestamp: 1745369987697
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
  sha256: 59a87161212abe8acc57d318b0cc8636eb834cdfdfddcf1f588b5493644b39a3
  md5: 9e60c55e725c20d23125a5f0dd69af5d
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgcc-ng ==15.1.0=*_3
  - libgomp 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  purls: []
  size: 824921
  timestamp: 1750808216066
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
  sha256: b0b0a5ee6ce645a09578fc1cb70c180723346f8a45fdb6d23b3520591c6d6996
  md5: e66f2b8ad787e7beb0f846e4bd7e8493
  depends:
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  purls: []
  size: 29033
  timestamp: 1750808224854
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-lib-1.11.1-hb9d3cd8_0.conda
  sha256: dc9c7d7a6c0e6639deee6fde2efdc7e119e7739a6b229fa5f9049a449bae6109
  md5: 8504a291085c9fb809b66cabd5834307
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libgpg-error >=1.55,<2.0a0
  license: LGPL-2.1-or-later
  purls: []
  size: 590353
  timestamp: 1747060639058
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
  sha256: 104f2341546e295d1136ab3010e81391bd3fd5be0f095db59266e8eba2082d37
  md5: 2ee6d71b72f75d50581f2f68e965efdb
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 171165
  timestamp: 1746228870846
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
  sha256: a9a0cba030778eb2944a1f235dba51e503b66f8be0ce6f55f745173a515c3644
  md5: 8f04c7aae6a46503bc36d1ed5abc8c7c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libgettextpo 0.24.1 h5888daf_0
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 37234
  timestamp: 1746228897993
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
  sha256: dc2752241fa3d9e40ce552c1942d0a4b5eeb93740c9723873f6fcf8d39ef8d2d
  md5: 928b8be80851f5d8ffb016f9c81dae7a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - libglx 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  purls: []
  size: 134712
  timestamp: 1731330998354
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
  sha256: a6b5cf4d443044bc9a0293dd12ca2015f0ebe5edfdc9c4abdde0b9947f9eb7bd
  md5: 072ab14a02164b7c0c089055368ff776
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.45,<10.46.0a0
  constrains:
  - glib 2.84.2 *_0
  license: LGPL-2.1-or-later
  purls: []
  size: 3955066
  timestamp: 1747836671118
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
  sha256: 1175f8a7a0c68b7f81962699751bb6574e6f07db4c9f72825f978e3016f46850
  md5: 434ca7e50e40f4918ab701e3facd59a0
  depends:
  - __glibc >=2.17,<3.0.a0
  license: LicenseRef-libglvnd
  purls: []
  size: 132463
  timestamp: 1731330968309
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
  sha256: 2d35a679624a93ce5b3e9dd301fff92343db609b79f0363e6d0ceb3a6478bfa7
  md5: c8013e438185f33b13814c5c488acd5c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - xorg-libx11 >=1.8.10,<2.0a0
  license: LicenseRef-libglvnd
  purls: []
  size: 75504
  timestamp: 1731330988898
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
  sha256: 43710ab4de0cd7ff8467abff8d11e7bb0e36569df04ce1c099d48601818f11d1
  md5: 3cd1a7238a0dd3d0860fdefc496cc854
  depends:
  - __glibc >=2.17,<3.0.a0
  license: GPL-3.0-only WITH GCC-exception-3.1
  purls: []
  size: 447068
  timestamp: 1750808138400
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.55-h3f2d84a_0.conda
  sha256: 697334de4786a1067ea86853e520c64dd72b11a05137f5b318d8a444007b5e60
  md5: 2bd47db5807daade8500ed7ca4c512a4
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  purls: []
  size: 312184
  timestamp: 1745575272035
- conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
  sha256: d14c016482e1409ae1c50109a9ff933460a50940d2682e745ab1c172b5282a69
  md5: 804ca9e91bcaea0824a341d55b1684f2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.13.4,<2.14.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 2423200
  timestamp: 1731374922090
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
  sha256: 18a4afe14f731bfb9cf388659994263904d20111e42f841e9eea1bb6f91f4ab4
  md5: e796ff8ddc598affdf7c173d6145f087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  purls: []
  size: 713084
  timestamp: 1740128065462
- conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
  sha256: 98b399287e27768bf79d48faba8a99a2289748c65cd342ca21033fab1860d4a4
  md5: 9fa334557db9f63da6c9285fd2a48638
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  purls: []
  size: 628947
  timestamp: 1745268527144
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
  sha256: f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8
  md5: 1a580f7796c7bf6393fddb8bbbde58dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 112894
  timestamp: 1749230047870
- conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
  sha256: 3aa92d4074d4063f2a162cd8ecb45dccac93e543e565c01a787e16a43501f7ee
  md5: c7e925f37e3b40d893459e625f6a53f1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 91183
  timestamp: 1748393666725
- conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
  sha256: ffb066ddf2e76953f92e06677021c73c85536098f1c21fcd15360dbc859e22e4
  md5: 68e52064ed3897463c0e958ab5c8f91b
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 218500
  timestamp: 1745825989535
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-2025.0.0-hdc3f47d_3.conda
  sha256: fe0e184141a3563d4c97134a1b7a60c66302cf0e2692d15d49c41382cdf61648
  md5: 3a88245058baa9d18ef4ea6df18ff63e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  purls: []
  size: 5698665
  timestamp: 1742046924817
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-batch-plugin-2025.0.0-h4d9b6c2_3.conda
  sha256: b4c61b3e8fc4d7090a94e3fd3936faf347eea07cac993417153dd99bd293c08d
  md5: 2e349bafc75b212879bf70ef80e0d08c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - tbb >=2021.13.0
  purls: []
  size: 111823
  timestamp: 1742046947746
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-plugin-2025.0.0-h4d9b6c2_3.conda
  sha256: ae72903e0718897b85aae2110d9bb1bfa9490b0496522e3735b65c771e7da0ea
  md5: 74d074a3ac7af3378e16bfa6ff9cba30
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - tbb >=2021.13.0
  purls: []
  size: 238973
  timestamp: 1742046961091
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-hetero-plugin-2025.0.0-h981d57b_3.conda
  sha256: b2c9ef97907f9c77817290bfb898897b476cc7ccf1737f0b1254437dda3d4903
  md5: 21f7997d68220d7356c1f80dc500bfad
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  purls: []
  size: 196083
  timestamp: 1742046974588
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-cpu-plugin-2025.0.0-hdc3f47d_3.conda
  sha256: 9f6613906386a0c679c9a683ca97a5a2070111d9ada4f115c1806d921313e32d
  md5: 3385f38d15c7aebcc3b453e4d8dfb0fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  purls: []
  size: 12419296
  timestamp: 1742046988488
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-gpu-plugin-2025.0.0-hdc3f47d_3.conda
  sha256: 8430f87a3cc65d3ef1ec8f9bfa990f6fb635601ad34ce08d70209099ff03f39c
  md5: f2d50e234edd843d9d695f7da34c7e96
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - ocl-icd >=2.3.2,<3.0a0
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  purls: []
  size: 10119530
  timestamp: 1742047030958
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-npu-plugin-2025.0.0-hdc3f47d_3.conda
  sha256: 37ec3e304bf14d2d7b7781c4b6a8b3a54deae90bc7275f6ae160589ef219bcef
  md5: f632cad865436394eebd41c3afa2cda3
  depends:
  - __glibc >=2.17,<3.0.a0
  - level-zero >=1.21.2,<2.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  purls: []
  size: 1092544
  timestamp: 1742047065987
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-ir-frontend-2025.0.0-h981d57b_3.conda
  sha256: 268716b5c1858c1fddd51d63c7fcd7f3544ef04f221371ab6a2f9c579ca001e4
  md5: 94f25cc6fe70f507897abb8e61603023
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  purls: []
  size: 206013
  timestamp: 1742047080381
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-onnx-frontend-2025.0.0-h0e684df_3.conda
  sha256: 5ce66c01f6ea365a497f488e8eecea8930b6a016f9809db7f33b8a1ebbe5644e
  md5: 7cd3272c3171c1d43ed1c2b3d6795269
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  purls: []
  size: 1668681
  timestamp: 1742047094228
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-paddle-frontend-2025.0.0-h0e684df_3.conda
  sha256: 826507ac4ea2d496bdbec02dd9e3c8ed2eab253daa9d7f9119a8bc05c516d026
  md5: 5b66cbc9965b429922b8e69cd4e464d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  purls: []
  size: 690226
  timestamp: 1742047109935
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-pytorch-frontend-2025.0.0-h5888daf_3.conda
  sha256: fda07e70a23aac329be68ae488b790f548d687807f0e47bae7129df34f0adb5b
  md5: a6ece96eff7f60b2559ba699156b0edf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  purls: []
  size: 1123885
  timestamp: 1742047125703
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-frontend-2025.0.0-h684f15b_3.conda
  sha256: e02990fccd4676e362a026acff3d706b5839ebf6ae681d56a2903f62a63e03ef
  md5: e1aeb108f4731db088782c8a20abf40a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - snappy >=1.2.1,<1.3.0a0
  purls: []
  size: 1313789
  timestamp: 1742047140816
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5888daf_3.conda
  sha256: 236569eb4d472d75412a3384c2aad92b006afed721feec23ca08730a25932da7
  md5: a6fe9c25b834988ac88651aff731dd31
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  purls: []
  size: 488142
  timestamp: 1742047155790
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
  sha256: 786d43678d6d1dc5f88a6bad2d02830cfd5a0184e84a8caa45694049f0e3ea5f
  md5: b64523fb87ac6f87f0790f324ad43046
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 312472
  timestamp: 1744330953241
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
  sha256: 0bd91de9b447a2991e666f284ae8c722ffb1d84acb594dbd0c031bd656fa32b2
  md5: 70e3400cbbfa03e96dcde7fc13e38c7b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 28424
  timestamp: 1749901812541
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
  sha256: c8f5dc929ba5fcee525a66777498e03bbcbfefc05a0773e5163bb08ac5122f1a
  md5: 37511c874cf3b8d0034c8d24e73c0884
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  purls: []
  size: 289506
  timestamp: 1750095629466
- conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
  sha256: 691af28446345674c6b3fb864d0e1a1574b6cc2f788e0f036d73a6b05dcf81cf
  md5: edb86556cf4a0c133e7932a1597ff236
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 3358788
  timestamp: 1745159546868
- conda: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.58.4-he92a37e_3.conda
  sha256: a45ef03e6e700cc6ac6c375e27904531cf8ade27eb3857e080537ff283fb0507
  md5: d27665b20bc4d074b86e628b3ba5ab8b
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - freetype >=2.13.3,<3.0a0
  - gdk-pixbuf >=2.42.12,<3.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libgcc >=13
  - libglib >=2.84.0,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libxml2 >=2.13.7,<2.14.0a0
  - pango >=1.56.3,<2.0a0
  constrains:
  - __glibc >=2.17
  license: LGPL-2.1-or-later
  purls: []
  size: 6543651
  timestamp: 1743368725313
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
  sha256: f709cbede3d4f3aee4e2f8d60bd9e256057f410bd60b8964cb8cf82ec1457573
  md5: ef1910918dd895516a769ed36b5b3a4e
  depends:
  - lame >=3.100,<3.101.0a0
  - libflac >=1.4.3,<1.5.0a0
  - libgcc-ng >=12
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 354372
  timestamp: 1695747735668
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
  sha256: 9a9e5bf30178f821d4f8de25eac0ae848915bfde6a78a66ae8b77d9c33d9d0e5
  md5: c7c4888059a8324e52de475d1e7bdc53
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  purls: []
  size: 919723
  timestamp: 1750925531920
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
  sha256: 7650837344b7850b62fdba02155da0b159cf472b9ab59eb7b472f7bd01dff241
  md5: 6d11a5edae89fe413c0569f16d308f5a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  purls: []
  size: 3896407
  timestamp: 1750808251302
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
  sha256: bbaea1ecf973a7836f92b8ebecc94d3c758414f4de39d2cc6818a3d10cb3216b
  md5: 57541755b5a51691955012b8e197c06c
  depends:
  - libstdcxx 15.1.0 h8f9b012_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  purls: []
  size: 29093
  timestamp: 1750808292700
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-257.7-h4e0b6ca_0.conda
  sha256: e26b22c0ae40fb6ad4356104d5fa4ec33fe8dd8a10e6aef36a9ab0c6a6f47275
  md5: 1e12c8aa74fa4c3166a9bdc135bc4abf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  - libgcrypt-lib >=1.11.1,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: LGPL-2.1-or-later
  purls: []
  size: 487969
  timestamp: 1750949895969
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
  sha256: 7fa6ddac72e0d803bb08e55090a8f2e71769f1eb7adbd5711bdd7789561601b1
  md5: e79a094918988bb1807462cd42c83962
  depends:
  - __glibc >=2.17,<3.0.a0
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  purls: []
  size: 429575
  timestamp: 1747067001268
- conda: https://conda.anaconda.org/conda-forge/linux-64/libudev1-257.7-hbe16f8c_0.conda
  sha256: 3fca2655f4cf2ce6b618a2b52e3dce92f27f429732b88080567d5bbeea404c82
  md5: 5a23e52bd654a5297bd3e247eebab5a3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 143533
  timestamp: 1750949902296
- conda: https://conda.anaconda.org/conda-forge/linux-64/libunwind-1.6.2-h9c3ff4c_0.tar.bz2
  sha256: f2ac872920833960e514ce9efd8f7c08ce66dd870738d73839d1bce1ac497de6
  md5: a730b2badd586580c5752cc73842e068
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: MIT
  license_family: MIT
  purls: []
  size: 75491
  timestamp: 1638450786937
- conda: https://conda.anaconda.org/conda-forge/linux-64/liburing-2.10-h84d6215_0.conda
  sha256: d1922de78ead6a9d19b7a4f82cf1fff7332e9012fd9968aa835c89888628d3d7
  md5: 1a11973f25f6168f4f6a65883cf7bb2a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 124944
  timestamp: 1748686602857
- conda: https://conda.anaconda.org/conda-forge/linux-64/libusb-1.0.29-h73b1eb8_0.conda
  sha256: 89c84f5b26028a9d0f5c4014330703e7dff73ba0c98f90103e9cef6b43a5323c
  md5: d17e3fb595a9f24fa9e149239a33475d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libudev1 >=257.4
  license: LGPL-2.1-or-later
  purls: []
  size: 89551
  timestamp: 1748856210075
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libva-2.22.0-h4f16b4b_2.conda
  sha256: e0df324fb02fa05a05824b8db886b06659432b5cff39495c59e14a37aa23d40f
  md5: 2c65566e79dc11318ce689c656fb551c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libdrm >=2.4.124,<2.5.0a0
  - libegl >=1.7.0,<2.0a0
  - libgcc >=13
  - libgl >=1.7.0,<2.0a0
  - libglx >=1.7.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - wayland >=1.23.1,<2.0a0
  - wayland-protocols
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 217567
  timestamp: 1740897682004
- conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
  sha256: 53080d72388a57b3c31ad5805c93a7328e46ff22fab7c44ad2a86d712740af33
  md5: 309dec04b70a3cc0f1e84a4013683bc0
  depends:
  - libgcc-ng >=9.3.0
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=9.3.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 286280
  timestamp: 1610609811627
- conda: https://conda.anaconda.org/conda-forge/linux-64/libvpx-1.14.1-hac33072_0.conda
  sha256: e7d2daf409c807be48310fcc8924e481b62988143f582eb3a58c5523a6763b13
  md5: cde393f461e0c169d9ffb2fc70f81c33
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 1022466
  timestamp: 1717859935011
- conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
  sha256: c45283fd3e90df5f0bd3dbcd31f59cdd2b001d424cf30a07223655413b158eaf
  md5: 63f790534398730f59e1b899c3644d4a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - libwebp 1.5.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 429973
  timestamp: 1734777489810
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
  sha256: 666c0c431b23c6cec6e492840b176dde533d48b7e6fb8883f5071223433776aa
  md5: 92ed62436b625154323d40d5f2f11dd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  purls: []
  size: 395888
  timestamp: 1727278577118
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
  sha256: a8043a46157511b3ceb6573a99952b5c0232313283f2d6a066cec7c8dcaed7d0
  md5: fedf6bfe5d21d21d2b1785ec00a8889a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.12,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  purls: []
  size: 707156
  timestamp: 1747911059945
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
  sha256: b0b3a96791fa8bb4ec030295e8c8bf2d3278f33c0f9ad540e73b5e538e6268e7
  md5: 14dbe05b929e329dbaa6f2d0aa19466d
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 690864
  timestamp: 1746634244154
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
  sha256: 47326f811392a5fd3055f0f773036c392d26fdb32e4d8e7a8197eed951489346
  md5: 9de5350a85c4a20c685259b889aa6393
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 167055
  timestamp: 1733741040117
- pypi: https://files.pythonhosted.org/packages/27/1a/1f68f9ba0c207934b35b86a8ca3aad8395a3d6dd7921c0686e23853ff5a9/mccabe-0.7.0-py2.py3-none-any.whl
  name: mccabe
  version: 0.7.0
  sha256: 6c2d30ab6be0e4a46919781807b4f0d834ebdd6c6e3dca0bda5a15f863427b6e
  requires_python: '>=3.6'
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
  sha256: 39c4700fb3fbe403a77d8cc27352fa72ba744db487559d5d44bf8411bb4ea200
  md5: c7f302fd11eeb0987a6a5e1f3aed6a21
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-only
  license_family: LGPL
  purls: []
  size: 491140
  timestamp: 1730581373280
- pypi: https://files.pythonhosted.org/packages/69/cb/b84afdb961dcf09b8e8c0238f068122d85480bfaac2c5c0b03120e497318/multidict-6.6.2-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl
  name: multidict
  version: 6.6.2
  sha256: c6c2d7686d2f9ecb18192455aa04345a05646f45a286d67b31b438eaf749a91e
  requires_dist:
  - typing-extensions>=4.1.0 ; python_full_version < '3.11'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/b4/7e/81ca3b074021ad9775e5cb97ebe0089c0f13684b066a750b7dc208438403/mypy-1.16.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl
  name: mypy
  version: 1.16.1
  sha256: 051e1677689c9d9578b9c7f4d206d763f9bbd95723cd1416fad50db49d52f359
  requires_dist:
  - typing-extensions>=4.6.0
  - mypy-extensions>=1.0.0
  - pathspec>=0.9.0
  - tomli>=1.1.0 ; python_full_version < '3.11'
  - psutil>=4.0 ; extra == 'dmypy'
  - setuptools>=50 ; extra == 'mypyc'
  - lxml ; extra == 'reports'
  - pip ; extra == 'install-types'
  - orjson ; extra == 'faster-cache'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/79/7b/2c79738432f5c924bef5071f933bcc9efd0473bac3b4aa584a6f7c1c8df8/mypy_extensions-1.1.0-py3-none-any.whl
  name: mypy-extensions
  version: 1.1.0
  sha256: 1be4cccdb0f2482337c4743e60421de3a356cd97508abadd57d47403e94f5505
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 891641
  timestamp: 1738195959188
- pypi: https://files.pythonhosted.org/packages/d2/1d/1b658dbd2b9fa9c4c9f32accbfc0205d532c8c6194dc0f2a4c0428e7128a/nodeenv-1.9.1-py2.py3-none-any.whl
  name: nodeenv
  version: 1.9.1
  sha256: ba11c9782d29c27c70ffbdda2d7415098754709be8a7056d79a737cd901155c9
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,!=3.6.*'
- conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.3-hb9d3cd8_0.conda
  sha256: 2254dae821b286fb57c61895f2b40e3571a070910fdab79a948ff703e1ea807b
  md5: 56f8947aa9d5cf37b0b3d43b83f34192
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - opencl-headers >=2024.10.24
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 106742
  timestamp: 1743700382939
- conda: https://conda.anaconda.org/conda-forge/linux-64/opencl-headers-2025.06.13-h5888daf_0.conda
  sha256: 2b6ce54174ec19110e1b3c37455f7cd138d0e228a75727a9bba443427da30a36
  md5: 45c3d2c224002d6d0d7769142b29f986
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 55357
  timestamp: 1749853464518
- conda: https://conda.anaconda.org/conda-forge/linux-64/openh264-2.6.0-hc22cd8d_0.conda
  sha256: 3f231f2747a37a58471c82a9a8a80d92b7fece9f3fce10901a5ac888ce00b747
  md5: b28cf020fd2dead0ca6d113608683842
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 731471
  timestamp: 1739400677213
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
  sha256: b4491077c494dbf0b5eaa6d87738c22f2154e9277e5293175ec187634bd808a0
  md5: de356753cfdbffcde5bb1e86e3aa6cd0
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 3117410
  timestamp: 1746223723843
- pypi: https://files.pythonhosted.org/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl
  name: packaging
  version: '25.0'
  sha256: 29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/pango-1.56.3-h9ac818e_1.conda
  sha256: 9c00bbc8871b9ce00d1a1f0c1a64f76c032cf16a56a28984b9bb59e46af3932d
  md5: 21899b96828014270bd24fd266096612
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.13.3,<3.0a0
  - fribidi >=1.0.10,<2.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.84.0,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  license: LGPL-2.1-or-later
  purls: []
  size: 453100
  timestamp: 1743352484196
- pypi: https://files.pythonhosted.org/packages/cc/20/ff623b09d963f88bfde16306a54e12ee5ea43e9b597108672ff3a408aad6/pathspec-0.12.1-py3-none-any.whl
  name: pathspec
  version: 0.12.1
  sha256: a0d503e138a4c123b27490a4f7beda6a01c6f288df0e4a8b79c7eb0dc7b4cc08
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
  sha256: 27c4014f616326240dcce17b5f3baca3953b6bc5f245ceb49c3fa1e6320571eb
  md5: b90bece58b4c2bf25969b70f3be42d25
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 1197308
  timestamp: 1745955064657
- conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.2-h29eaf8c_0.conda
  sha256: 6cb261595b5f0ae7306599f2bb55ef6863534b6d4d1bc0dcfdfa5825b0e4e53d
  md5: 39b4228a867772d610c02e06f939a5b8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 402222
  timestamp: 1749552884791
- pypi: https://files.pythonhosted.org/packages/fe/39/979e8e21520d4e47a0bbe349e2713c0aac6f3d853d0e5b34d76206c439aa/platformdirs-4.3.8-py3-none-any.whl
  name: platformdirs
  version: 4.3.8
  sha256: ff7059bb7eb1179e2685604f4aaf157cfd9535242bd23742eadc3c13542139b4
  requires_dist:
  - furo>=2024.8.6 ; extra == 'docs'
  - proselint>=0.14 ; extra == 'docs'
  - sphinx-autodoc-typehints>=3 ; extra == 'docs'
  - sphinx>=8.1.3 ; extra == 'docs'
  - appdirs==1.4.4 ; extra == 'test'
  - covdefaults>=2.3 ; extra == 'test'
  - pytest-cov>=6 ; extra == 'test'
  - pytest-mock>=3.14 ; extra == 'test'
  - pytest>=8.3.4 ; extra == 'test'
  - mypy>=1.14.1 ; extra == 'type'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/54/20/4d324d65cc6d9205fabedc306948156824eb9f0ee1633355a8f7ec5c66bf/pluggy-1.6.0-py3-none-any.whl
  name: pluggy
  version: 1.6.0
  sha256: e920276dd6813095e9377c0bc5566d94c932c33b27a3e3945d8389c374dd4746
  requires_dist:
  - pre-commit ; extra == 'dev'
  - tox ; extra == 'dev'
  - pytest ; extra == 'testing'
  - pytest-benchmark ; extra == 'testing'
  - coverage ; extra == 'testing'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/88/74/a88bf1b1efeae488a0c0b7bdf71429c313722d1fc0f377537fbe554e6180/pre_commit-4.2.0-py2.py3-none-any.whl
  name: pre-commit
  version: 4.2.0
  sha256: a009ca7205f1eb497d10b845e52c838a98b6cdd2102a6c8e4540e94ee75c58bd
  requires_dist:
  - cfgv>=2.0.0
  - identify>=1.0.0
  - nodeenv>=0.11.1
  - pyyaml>=5.1
  - virtualenv>=20.10.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/af/81/b324c44ae60c56ef12007105f1460d5c304b0626ab0cc6b07c8f2a9aa0b8/propcache-0.3.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: propcache
  version: 0.3.2
  sha256: 4c1396592321ac83157ac03a2023aa6cc4a3cc3cfdecb71090054c09e5a7cce3
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
  sha256: 9c88f8c64590e9567c6c80823f0328e58d3b1efb0e1c539c0315ceca764e0973
  md5: b3c17d95b5a10c6e64a21fa17573e70e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 8252
  timestamp: 1726802366959
- conda: https://conda.anaconda.org/conda-forge/linux-64/pugixml-1.15-h3f63f65_0.conda
  sha256: 23c98a5000356e173568dc5c5770b53393879f946f3ace716bbdefac2a8b23d2
  md5: b11a4c6bf6f6f44e5e143f759ffa2087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 118488
  timestamp: 1736601364156
- conda: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hac146a9_1.conda
  sha256: d2377bb571932f2373f593b7b2fc3b9728dc6ae5b993b1b65d7f2c8bb39a0b49
  md5: 66b1fa9608d8836e25f9919159adc9c6
  depends:
  - __glibc >=2.17,<3.0.a0
  - dbus >=1.13.6,<2.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libiconv >=1.18,<2.0a0
  - libsndfile >=1.2.2,<1.3.0a0
  - libsystemd0 >=257.4
  - libxcb >=1.17.0,<2.0a0
  constrains:
  - pulseaudio 17.0 *_1
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 764231
  timestamp: 1742507189208
- pypi: https://files.pythonhosted.org/packages/d7/27/a58ddaf8c588a3ef080db9d0b7e0b97215cee3a45df74f3a94dbbf5c893a/pycodestyle-2.14.0-py2.py3-none-any.whl
  name: pycodestyle
  version: 2.14.0
  sha256: dd6bf7cb4ee77f8e016f9c8e74a35ddd9f67e1d5fd4184d86c3b98e07099f42d
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/c2/2f/81d580a0fb83baeb066698975cb14a618bdbed7720678566f1b046a95fe8/pyflakes-3.4.0-py2.py3-none-any.whl
  name: pyflakes
  version: 3.4.0
  sha256: f742a7dbd0d9cb9ea41e9a24a918996e8170c799fa528688d40dd582c8265f4f
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/c7/21/705964c7812476f378728bdf590ca4b771ec72385c533964653c68e86bdc/pygments-2.19.2-py3-none-any.whl
  name: pygments
  version: 2.19.2
  sha256: 86540386c03d588bb81d44bc3928634ff26449851e99741617ecb9037ee5ec0b
  requires_dist:
  - colorama>=0.4.6 ; extra == 'windows-terminal'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/29/16/c8a903f4c4dffe7a12843191437d7cd8e32751d5de349d45d3fe69544e87/pytest-8.4.1-py3-none-any.whl
  name: pytest
  version: 8.4.1
  sha256: 539c70ba6fcead8e78eebbf1115e8b589e7565830d7d006a8723f19ac8a0afb7
  requires_dist:
  - colorama>=0.4 ; sys_platform == 'win32'
  - exceptiongroup>=1 ; python_full_version < '3.11'
  - iniconfig>=1
  - packaging>=20
  - pluggy>=1.5,<2
  - pygments>=2.7.2
  - tomli>=1 ; python_full_version < '3.11'
  - argcomplete ; extra == 'dev'
  - attrs>=19.2 ; extra == 'dev'
  - hypothesis>=3.56 ; extra == 'dev'
  - mock ; extra == 'dev'
  - requests ; extra == 'dev'
  - setuptools ; extra == 'dev'
  - xmlschema ; extra == 'dev'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/30/05/ce271016e351fddc8399e546f6e23761967ee09c8c568bbfbecb0c150171/pytest_asyncio-1.0.0-py3-none-any.whl
  name: pytest-asyncio
  version: 1.0.0
  sha256: 4f024da9f1ef945e680dc68610b52550e36590a67fd31bb3b4943979a1f90ef3
  requires_dist:
  - pytest>=8.2,<9
  - typing-extensions>=4.12 ; python_full_version < '3.10'
  - sphinx>=5.3 ; extra == 'docs'
  - sphinx-rtd-theme>=1 ; extra == 'docs'
  - coverage>=6.2 ; extra == 'testing'
  - hypothesis>=5.7.1 ; extra == 'testing'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
  build_number: 102
  sha256: c2cdcc98ea3cbf78240624e4077e164dc9d5588eefb044b4097c3df54d24d504
  md5: 89e07d92cf50743886f41638d58c4328
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  purls: []
  size: 33273132
  timestamp: 1750064035176
  python_site_packages_path: lib/python3.13/site-packages
- pypi: https://files.pythonhosted.org/packages/5f/ed/539768cf28c661b5b068d66d96a2f155c4971a5d55684a514c1a0e0dec2f/python_dotenv-1.1.1-py3-none-any.whl
  name: python-dotenv
  version: 1.1.1
  sha256: 31f23644fe2602f88ff55e1f5c79ba497e01224ee7737937930c448e4d0e24dc
  requires_dist:
  - click>=5.0 ; extra == 'cli'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
  build_number: 7
  sha256: 0595134584589064f56e67d3de1d8fcbb673a972946bce25fb593fb092fdcd97
  md5: e84b44e6300f1703cb25d29120c5b1d8
  constrains:
  - python 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 6988
  timestamp: 1745258852285
- pypi: https://files.pythonhosted.org/packages/04/24/b7721e4845c2f162d26f50521b825fb061bc0a5afcf9a386840f23ea19fa/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pyyaml
  version: 6.0.2
  sha256: 70b189594dbe54f75ab3a1acec5f1e3faa7e8cf2f1e08d9b561cb41b845f69d5
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2-2.32.54-h3f2d84a_0.conda
  sha256: 7cd82ca1d1989de6ac28e72ba0bfaae1c055278f931b0c7ef51bb1abba3ddd2f
  md5: 91f8537d64c4d52cbbb2910e8bd61bd2
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - sdl3 >=3.2.10,<4.0a0
  - libgl >=1.7.0,<2.0a0
  - libegl >=1.7.0,<2.0a0
  license: Zlib
  purls: []
  size: 587053
  timestamp: 1745799881584
- conda: https://conda.anaconda.org/conda-forge/linux-64/sdl3-3.2.16-he3e324a_0.conda
  sha256: 7fe5ff84801d1ad0713efbb1a9c39c3c4245ccee5586bd62fc4604d0f23ce0df
  md5: c3ab38fdbcf36625620c9a4df786320a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - dbus >=1.16.2,<2.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  - xorg-libxcursor >=1.2.3,<2.0a0
  - libxkbcommon >=1.10.0,<2.0a0
  - libgl >=1.7.0,<2.0a0
  - wayland >=1.23.1,<2.0a0
  - libusb >=1.0.29,<2.0a0
  - libudev1 >=257.6
  - libegl >=1.7.0,<2.0a0
  - liburing >=2.10,<2.11.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxscrnsaver >=1.2.4,<2.0a0
  - libdrm >=2.4.124,<2.5.0a0
  - libunwind >=1.6.2,<1.7.0a0
  license: Zlib
  purls: []
  size: 1941645
  timestamp: 1748911618893
- pypi: https://files.pythonhosted.org/packages/01/a1/fc4856bd02d2097324fb7ce05b3021fb850f864b83ca765f6e37e92ff8ca/sentry_sdk-2.32.0-py2.py3-none-any.whl
  name: sentry-sdk
  version: 2.32.0
  sha256: ****************************************************************
  requires_dist:
  - urllib3>=1.26.11
  - certifi
  - aiohttp>=3.5 ; extra == 'aiohttp'
  - anthropic>=0.16 ; extra == 'anthropic'
  - arq>=0.23 ; extra == 'arq'
  - asyncpg>=0.23 ; extra == 'asyncpg'
  - apache-beam>=2.12 ; extra == 'beam'
  - bottle>=0.12.13 ; extra == 'bottle'
  - celery>=3 ; extra == 'celery'
  - celery-redbeat>=2 ; extra == 'celery-redbeat'
  - chalice>=1.16.0 ; extra == 'chalice'
  - clickhouse-driver>=0.2.0 ; extra == 'clickhouse-driver'
  - django>=1.8 ; extra == 'django'
  - falcon>=1.4 ; extra == 'falcon'
  - fastapi>=0.79.0 ; extra == 'fastapi'
  - flask>=0.11 ; extra == 'flask'
  - blinker>=1.1 ; extra == 'flask'
  - markupsafe ; extra == 'flask'
  - grpcio>=1.21.1 ; extra == 'grpcio'
  - protobuf>=3.8.0 ; extra == 'grpcio'
  - httpcore[http2]==1.* ; extra == 'http2'
  - httpx>=0.16.0 ; extra == 'httpx'
  - huey>=2 ; extra == 'huey'
  - huggingface-hub>=0.22 ; extra == 'huggingface-hub'
  - langchain>=0.0.210 ; extra == 'langchain'
  - launchdarkly-server-sdk>=9.8.0 ; extra == 'launchdarkly'
  - litestar>=2.0.0 ; extra == 'litestar'
  - loguru>=0.5 ; extra == 'loguru'
  - openai>=1.0.0 ; extra == 'openai'
  - tiktoken>=0.3.0 ; extra == 'openai'
  - openfeature-sdk>=0.7.1 ; extra == 'openfeature'
  - opentelemetry-distro>=0.35b0 ; extra == 'opentelemetry'
  - opentelemetry-distro ; extra == 'opentelemetry-experimental'
  - pure-eval ; extra == 'pure-eval'
  - executing ; extra == 'pure-eval'
  - asttokens ; extra == 'pure-eval'
  - pymongo>=3.1 ; extra == 'pymongo'
  - pyspark>=2.4.4 ; extra == 'pyspark'
  - quart>=0.16.1 ; extra == 'quart'
  - blinker>=1.1 ; extra == 'quart'
  - rq>=0.6 ; extra == 'rq'
  - sanic>=0.8 ; extra == 'sanic'
  - sqlalchemy>=1.2 ; extra == 'sqlalchemy'
  - starlette>=0.19.1 ; extra == 'starlette'
  - starlite>=1.48 ; extra == 'starlite'
  - statsig>=0.55.3 ; extra == 'statsig'
  - tornado>=6 ; extra == 'tornado'
  - unleashclient>=6.0.1 ; extra == 'unleash'
  requires_python: '>=3.6'
- conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
  sha256: ec91e86eeb2c6bbf09d51351b851e945185d70661d2ada67204c9a6419d282d3
  md5: 3b3e64af585eadfb52bb90b553db5edf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 42739
  timestamp: 1733501881851
- conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
  sha256: fb4b97a3fd259eff4849b2cfe5678ced0c5792b697eb1f7bcd93a4230e90e80e
  md5: 0096882bd623e6cc09e8bf920fc8fb47
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 2750235
  timestamp: 1742907589246
- conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2022.1.0-h4ce085d_0.conda
  sha256: b2819dd77faee0ea1f14774b603db33da44c14f7662982d4da4bbe76ac8a8976
  md5: f0afd0c7509f6c1b8d77ee64d7ba64b8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libhwloc >=2.11.2,<2.11.3.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 179639
  timestamp: 1743578685131
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
  sha256: a84ff687119e6d8752346d1d408d5cf360dee0badd487a472aa8ddedfdc219e1
  md5: a0116df4f4ed05c303811a837d5b39d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3285204
  timestamp: 1748387766691
- pypi: https://files.pythonhosted.org/packages/69/e0/552843e0d356fbb5256d21449fa957fa4eff3bbc135a74a691ee70c7c5da/typing_extensions-4.14.0-py3-none-any.whl
  name: typing-extensions
  version: 4.14.0
  sha256: a1514509136dd0b477638fc68d6a91497af5076466ad0fa6c338e44e359944af
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  purls: []
  size: 122968
  timestamp: 1742727099393
- pypi: https://files.pythonhosted.org/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl
  name: urllib3
  version: 2.5.0
  sha256: e6b01673c0fa6a13e374b50871808eb3bf7046c4b125b216f6bf1cc604cff0dc
  requires_dist:
  - brotli>=1.0.9 ; platform_python_implementation == 'CPython' and extra == 'brotli'
  - brotlicffi>=0.8.0 ; platform_python_implementation != 'CPython' and extra == 'brotli'
  - h2>=4,<5 ; extra == 'h2'
  - pysocks>=1.5.6,!=1.5.7,<2.0 ; extra == 'socks'
  - zstandard>=0.18.0 ; extra == 'zstd'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/f3/40/b1c265d4b2b62b58576588510fc4d1fe60a86319c8de99fd8e9fec617d2c/virtualenv-20.31.2-py3-none-any.whl
  name: virtualenv
  version: 20.31.2
  sha256: 36efd0d9650ee985f0cad72065001e66d49a6f24eb44d98980f630686243cf11
  requires_dist:
  - distlib>=0.3.7,<1
  - filelock>=3.12.2,<4
  - importlib-metadata>=6.6 ; python_full_version < '3.8'
  - platformdirs>=3.9.1,<5
  - furo>=2023.7.26 ; extra == 'docs'
  - proselint>=0.13 ; extra == 'docs'
  - sphinx>=7.1.2,!=7.3 ; extra == 'docs'
  - sphinx-argparse>=0.4 ; extra == 'docs'
  - sphinxcontrib-towncrier>=0.2.1a0 ; extra == 'docs'
  - towncrier>=23.6 ; extra == 'docs'
  - covdefaults>=2.3 ; extra == 'test'
  - coverage-enable-subprocess>=1 ; extra == 'test'
  - coverage>=7.2.7 ; extra == 'test'
  - flaky>=3.7 ; extra == 'test'
  - packaging>=23.1 ; extra == 'test'
  - pytest-env>=0.8.2 ; extra == 'test'
  - pytest-freezer>=0.4.8 ; (python_full_version >= '3.13' and platform_python_implementation == 'CPython' and sys_platform == 'win32' and extra == 'test') or (platform_python_implementation == 'GraalVM' and extra == 'test') or (platform_python_implementation == 'PyPy' and extra == 'test')
  - pytest-mock>=3.11.1 ; extra == 'test'
  - pytest-randomly>=3.12 ; extra == 'test'
  - pytest-timeout>=2.1 ; extra == 'test'
  - pytest>=7.4 ; extra == 'test'
  - setuptools>=68 ; extra == 'test'
  - time-machine>=2.10 ; platform_python_implementation == 'CPython' and extra == 'test'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
  sha256: 73d809ec8056c2f08e077f9d779d7f4e4c2b625881cad6af303c33dc1562ea01
  md5: a37843723437ba75f42c9270ffe800b1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 321099
  timestamp: 1745806602179
- conda: https://conda.anaconda.org/conda-forge/noarch/wayland-protocols-1.45-hd8ed1ab_0.conda
  sha256: ****************************************************************
  md5: 6db9be3b67190229479780eeeee1b35b
  license: MIT
  license_family: MIT
  purls: []
  size: 138011
  timestamp: 1749836220507
- conda: https://conda.anaconda.org/conda-forge/linux-64/x264-1!164.3095-h166bdaf_2.tar.bz2
  sha256: 175315eb3d6ea1f64a6ce470be00fa2ee59980108f246d3072ab8b977cb048a5
  md5: 6c99772d483f566d59e25037fea2c4b1
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 897548
  timestamp: 1660323080555
- conda: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
  sha256: 76c7405bcf2af639971150f342550484efac18219c0203c5ee2e38b8956fe2a0
  md5: e7f6ed84d4623d52ee581325c1587a6b
  depends:
  - libgcc-ng >=10.3.0
  - libstdcxx-ng >=10.3.0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 3357188
  timestamp: 1646609687141
- conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
  sha256: a5d4af601f71805ec67403406e147c48d6bad7aaeae92b0622b7e2396842d3fe
  md5: 397a013c2dc5145a70737871aaa87e98
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.12,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 392406
  timestamp: 1749375847832
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
  sha256: c12396aabb21244c212e488bbdc4abcdef0b7404b15761d9329f5a4a39113c4b
  md5: fb901ff28063514abb6046c9ec2c4a45
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 58628
  timestamp: 1734227592886
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
  sha256: 277841c43a39f738927145930ff963c5ce4c4dacf66637a3d95d802a64173250
  md5: 1c74ff8c35dcadf952a16f752ca5aa49
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.2,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 27590
  timestamp: 1741896361728
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
  sha256: 51909270b1a6c5474ed3978628b341b4d4472cd22610e5f22b506855a5e20f67
  md5: db038ce880f100acc74dba10302b5630
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 835896
  timestamp: 1741901112627
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
  sha256: ed10c9283974d311855ae08a16dfd7e56241fac632aec3b92e3cfe73cff31038
  md5: f6ebe2cb3f82ba6c057dde5d9debe4f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 14780
  timestamp: 1734229004433
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
  sha256: 832f538ade441b1eee863c8c91af9e69b356cd3e9e1350fff4fe36cc573fc91a
  md5: 2ccd714aa2242315acaf0a67faea780b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 32533
  timestamp: 1730908305254
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
  sha256: 6b250f3e59db07c2514057944a3ea2044d6a8cdde8a47b6497c254520fade1ee
  md5: 8035c64cb77ed555e3f150b7b3972480
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 19901
  timestamp: 1727794976192
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
  sha256: da5dc921c017c05f38a38bd75245017463104457b63a1ce633ed41f214159c14
  md5: febbab7d15033c913d53c7a2c102309d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 50060
  timestamp: 1727752228921
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
  sha256: 2fef37e660985794617716eb915865ce157004a4d567ed35ec16514960ae9271
  md5: 4bdb303603e9821baf5fe5fdff1dc8f8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 19575
  timestamp: 1727794961233
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
  sha256: 044c7b3153c224c6cedd4484dd91b389d2d7fd9c776ad0f4a34f099b3389f4a1
  md5: 96d57aba173e878a2089d5638016dc5e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 33005
  timestamp: 1734229037766
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxscrnsaver-1.2.4-hb9d3cd8_0.conda
  sha256: 58e8fc1687534124832d22e102f098b5401173212ac69eb9fd96b16a3e2c8cb2
  md5: 303f7a0e9e0cd7d250bb6b952cecda90
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 14412
  timestamp: 1727899730073
- pypi: https://files.pythonhosted.org/packages/af/44/46407d7f7a56e9a85a4c207724c9f2c545c060380718eea9088f222ba697/yarl-1.20.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: yarl
  version: 1.20.1
  sha256: d1a4fbb50e14396ba3d375f68bfe02215d8e7bc3ec49da8341fe3157f59d2ff5
  requires_dist:
  - idna>=2.0
  - multidict>=4.0
  - propcache>=0.2.1
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
  sha256: a4166e3d8ff4e35932510aaff7aa90772f84b4d07e9f6f83c614cba7ceefe0eb
  md5: 6432cb5d4ac0046c3ac0a8a0f95842f9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 567578
  timestamp: 1742433379869
